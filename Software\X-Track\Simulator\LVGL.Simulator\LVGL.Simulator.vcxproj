<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3CA6E070-4AC1-475E-BB17-CF29AE4806DF}</ProjectGuid>
    <RootNamespace>LVGL</RootNamespace>
    <MileProjectType>ConsoleApplication</MileProjectType>
    <MileProjectManifestFile>LVGL.Simulator.manifest</MileProjectManifestFile>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <EnableASAN>true</EnableASAN>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <EnableASAN>false</EnableASAN>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <EnableASAN>true</EnableASAN>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <EnableASAN>false</EnableASAN>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <EnableASAN>true</EnableASAN>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <EnableASAN>false</EnableASAN>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <EnableASAN>true</EnableASAN>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <EnableASAN>false</EnableASAN>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="Mile.Project\Mile.Project.Cpp.props" />
  <Import Project="Mile.Project\Mile.Project.Cpp.VC-LTL.props" />
  <PropertyGroup>
    <IncludePath>$(MSBuildThisFileDirectory);$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization Condition="'$(Configuration)'=='Release'">MinSpace</Optimization>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">$(ProjectDir)..\..\USER\App</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">$(ProjectDir)..\..\USER\App</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)..\..\USER\App;$(ProjectDir)..\..\USER\App\Utils\ArduinoJson\src</AdditionalIncludeDirectories>
      <DebugInformationFormat Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">ProgramDatabase</DebugInformationFormat>
      <DebugInformationFormat Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ProgramDatabase</DebugInformationFormat>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">true</MultiProcessorCompilation>
      <MultiProcessorCompilation Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</MultiProcessorCompilation>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\USER\App\App.h" />
    <ClInclude Include="..\..\USER\App\Common\DataProc\DataProc.h" />
    <ClInclude Include="..\..\USER\App\Common\DataProc\DataProc_Def.h" />
    <ClInclude Include="..\..\USER\App\Common\HAL\HAL.h" />
    <ClInclude Include="..\..\USER\App\Common\HAL\HAL_Def.h" />
    <ClInclude Include="..\..\USER\App\Common\Music\ToneMap.h" />
    <ClInclude Include="..\..\USER\App\Config\Config.h" />
    <ClInclude Include="..\..\USER\App\Pages\AppFactory.h" />
    <ClInclude Include="..\..\USER\App\Pages\Dialplate\Dialplate.h" />
    <ClInclude Include="..\..\USER\App\Pages\Dialplate\DialplateModel.h" />
    <ClInclude Include="..\..\USER\App\Pages\Dialplate\DialplateView.h" />
    <ClInclude Include="..\..\USER\App\Pages\LiveMap\LiveMap.h" />
    <ClInclude Include="..\..\USER\App\Pages\LiveMap\LiveMapModel.h" />
    <ClInclude Include="..\..\USER\App\Pages\LiveMap\LiveMapView.h" />
    <ClInclude Include="..\..\USER\App\Pages\Page.h" />
    <ClInclude Include="..\..\USER\App\Pages\Startup\Startup.h" />
    <ClInclude Include="..\..\USER\App\Pages\Startup\StartupModel.h" />
    <ClInclude Include="..\..\USER\App\Pages\Startup\StartupView.h" />
    <ClInclude Include="..\..\USER\App\Pages\StatusBar\StatusBar.h" />
    <ClInclude Include="..\..\USER\App\Pages\SystemInfos\SystemInfos.h" />
    <ClInclude Include="..\..\USER\App\Pages\SystemInfos\SystemInfosModel.h" />
    <ClInclude Include="..\..\USER\App\Pages\SystemInfos\SystemInfosView.h" />
    <ClInclude Include="..\..\USER\App\Pages\_Template\Template.h" />
    <ClInclude Include="..\..\USER\App\Pages\_Template\TemplateModel.h" />
    <ClInclude Include="..\..\USER\App\Pages\_Template\TemplateView.h" />
    <ClInclude Include="..\..\USER\App\Resource\ResourcePool.h" />
    <ClInclude Include="..\..\USER\App\Utils\ArduinoJson\ArduinoJson.h" />
    <ClInclude Include="..\..\USER\App\Utils\DataCenter\Account.h" />
    <ClInclude Include="..\..\USER\App\Utils\DataCenter\DataCenter.h" />
    <ClInclude Include="..\..\USER\App\Utils\DataCenter\DataCenterLog.h" />
    <ClInclude Include="..\..\USER\App\Utils\DataCenter\PingPongBuffer\PingPongBuffer.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\FilterBase.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\Filters.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\MedianFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\LowpassFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\MedianQueueFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\HysteresisFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\Filters\SlidingFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\GPX\GPX.h" />
    <ClInclude Include="..\..\USER\App\Utils\GPX_Parser\GPX_Parser.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_anim_label\lv_anim_label.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_ext\lv_anim_timeline_wrapper.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_ext\lv_obj_ext_func.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\lv_img_png.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\crc32.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inffast.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inffixed.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inflate.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inftrees.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\PNGdec.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\zconf.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\zlib.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\zutil.h" />
    <ClInclude Include="..\..\USER\App\Utils\lv_poly_line\lv_poly_line.h" />
    <ClInclude Include="..\..\USER\App\Utils\MapConv\GPS_Transform\GPS_Transform.h" />
    <ClInclude Include="..\..\USER\App\Utils\MapConv\MapConv.h" />
    <ClInclude Include="..\..\USER\App\Utils\MapConv\TileSystem\TileSystem.h" />
    <ClInclude Include="..\..\USER\App\Utils\PageManager\PageBase.h" />
    <ClInclude Include="..\..\USER\App\Utils\PageManager\PageFactory.h" />
    <ClInclude Include="..\..\USER\App\Utils\PageManager\PageManager.h" />
    <ClInclude Include="..\..\USER\App\Utils\PageManager\PM_Log.h" />
    <ClInclude Include="..\..\USER\App\Utils\PointContainer\PointContainer.h" />
    <ClInclude Include="..\..\USER\App\Utils\ResourceManager\ResourceManager.h" />
    <ClInclude Include="..\..\USER\App\Utils\StorageService\StorageService.h" />
    <ClInclude Include="..\..\USER\App\Utils\Stream\Print.h" />
    <ClInclude Include="..\..\USER\App\Utils\Stream\Printable.h" />
    <ClInclude Include="..\..\USER\App\Utils\Stream\Stream.h" />
    <ClInclude Include="..\..\USER\App\Utils\TileConv\TileConv.h" />
    <ClInclude Include="..\..\USER\App\Utils\Time\Time.h" />
    <ClInclude Include="..\..\USER\App\Utils\Time\TimeLib.h" />
    <ClInclude Include="..\..\USER\App\Utils\TonePlayer\TonePlayer.h" />
    <ClInclude Include="..\..\USER\App\Utils\TrackFilter\TrackFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\TrackFilter\TrackLineFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\TrackFilter\TrackPointFilter.h" />
    <ClInclude Include="..\..\USER\App\Utils\WString\dtostrf.h" />
    <ClInclude Include="..\..\USER\App\Utils\WString\WCharacter.h" />
    <ClInclude Include="..\..\USER\App\Utils\WString\WString.h" />
    <ClInclude Include="..\..\USER\App\Version.h" />
    <ClInclude Include="HAL\HAL.h" />
    <ClInclude Include="lvgl\examples\anim\lv_example_anim.h" />
    <ClInclude Include="lvgl\examples\get_started\lv_example_get_started.h" />
    <ClInclude Include="lvgl\examples\layouts\flex\lv_example_flex.h" />
    <ClInclude Include="lvgl\examples\layouts\grid\lv_example_grid.h" />
    <ClInclude Include="lvgl\examples\layouts\lv_example_layout.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_disp_template.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_fs_template.h" />
    <ClInclude Include="lvgl\examples\porting\lv_port_indev_template.h" />
    <ClInclude Include="lvgl\examples\scroll\lv_example_scroll.h" />
    <ClInclude Include="lvgl\examples\styles\lv_example_style.h" />
    <ClInclude Include="lvgl\examples\widgets\lv_example_widgets.h" />
    <ClInclude Include="lvgl\examples\lv_examples.h" />
    <ClInclude Include="lvgl\src\core\lv_disp.h" />
    <ClInclude Include="lvgl\src\core\lv_event.h" />
    <ClInclude Include="lvgl\src\core\lv_group.h" />
    <ClInclude Include="lvgl\src\core\lv_indev.h" />
    <ClInclude Include="lvgl\src\core\lv_indev_scroll.h" />
    <ClInclude Include="lvgl\src\core\lv_obj.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_class.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_draw.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_pos.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_scroll.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_style.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_style_gen.h" />
    <ClInclude Include="lvgl\src\core\lv_obj_tree.h" />
    <ClInclude Include="lvgl\src\core\lv_refr.h" />
    <ClInclude Include="lvgl\src\core\lv_theme.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_arc.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_img.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_label.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_line.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_mask.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_rect.h" />
    <ClInclude Include="lvgl\src\draw\lv_draw_triangle.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_buf.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_cache.h" />
    <ClInclude Include="lvgl\src\draw\lv_img_decoder.h" />
    <ClInclude Include="lvgl\src\draw\sw\lv_draw_sw.h" />
    <ClInclude Include="lvgl\src\extra\layouts\flex\lv_flex.h" />
    <ClInclude Include="lvgl\src\extra\layouts\grid\lv_grid.h" />
    <ClInclude Include="lvgl\src\extra\layouts\lv_layouts.h" />
    <ClInclude Include="lvgl\src\extra\libs\lv_libs.h" />
    <ClInclude Include="lvgl\src\extra\libs\png\lodepng.h" />
    <ClInclude Include="lvgl\src\extra\libs\png\lv_png.h" />
    <ClInclude Include="lvgl\src\extra\others\lv_others.h" />
    <ClInclude Include="lvgl\src\extra\others\monkey\lv_monkey.h" />
    <ClInclude Include="lvgl\src\extra\others\snapshot\lv_snapshot.h" />
    <ClInclude Include="lvgl\src\extra\themes\basic\lv_theme_basic.h" />
    <ClInclude Include="lvgl\src\extra\themes\default\lv_theme_default.h" />
    <ClInclude Include="lvgl\src\extra\themes\lv_themes.h" />
    <ClInclude Include="lvgl\src\extra\themes\mono\lv_theme_mono.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_arrow.h" />
    <ClInclude Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_dropdown.h" />
    <ClInclude Include="lvgl\src\extra\widgets\chart\lv_chart.h" />
    <ClInclude Include="lvgl\src\extra\widgets\colorwheel\lv_colorwheel.h" />
    <ClInclude Include="lvgl\src\extra\widgets\imgbtn\lv_imgbtn.h" />
    <ClInclude Include="lvgl\src\extra\widgets\keyboard\lv_keyboard.h" />
    <ClInclude Include="lvgl\src\extra\widgets\led\lv_led.h" />
    <ClInclude Include="lvgl\src\extra\widgets\list\lv_list.h" />
    <ClInclude Include="lvgl\src\extra\widgets\menu\lv_menu.h" />
    <ClInclude Include="lvgl\src\extra\widgets\meter\lv_meter.h" />
    <ClInclude Include="lvgl\src\extra\widgets\msgbox\lv_msgbox.h" />
    <ClInclude Include="lvgl\src\extra\widgets\span\lv_span.h" />
    <ClInclude Include="lvgl\src\extra\widgets\spinbox\lv_spinbox.h" />
    <ClInclude Include="lvgl\src\extra\widgets\spinner\lv_spinner.h" />
    <ClInclude Include="lvgl\src\extra\widgets\tabview\lv_tabview.h" />
    <ClInclude Include="lvgl\src\extra\widgets\tileview\lv_tileview.h" />
    <ClInclude Include="lvgl\src\extra\widgets\win\lv_win.h" />
    <ClInclude Include="lvgl\src\extra\widgets\lv_widgets.h" />
    <ClInclude Include="lvgl\src\extra\lv_extra.h" />
    <ClInclude Include="lvgl\src\font\lv_font.h" />
    <ClInclude Include="lvgl\src\font\lv_font_fmt_txt.h" />
    <ClInclude Include="lvgl\src\font\lv_font_loader.h" />
    <ClInclude Include="lvgl\src\font\lv_symbol_def.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_disp.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_indev.h" />
    <ClInclude Include="lvgl\src\hal\lv_hal_tick.h" />
    <ClInclude Include="lvgl\src\misc\lv_anim.h" />
    <ClInclude Include="lvgl\src\misc\lv_anim_timeline.h" />
    <ClInclude Include="lvgl\src\misc\lv_area.h" />
    <ClInclude Include="lvgl\src\misc\lv_assert.h" />
    <ClInclude Include="lvgl\src\misc\lv_async.h" />
    <ClInclude Include="lvgl\src\misc\lv_bidi.h" />
    <ClInclude Include="lvgl\src\misc\lv_color.h" />
    <ClInclude Include="lvgl\src\misc\lv_fs.h" />
    <ClInclude Include="lvgl\src\misc\lv_gc.h" />
    <ClInclude Include="lvgl\src\misc\lv_ll.h" />
    <ClInclude Include="lvgl\src\misc\lv_log.h" />
    <ClInclude Include="lvgl\src\misc\lv_math.h" />
    <ClInclude Include="lvgl\src\misc\lv_mem.h" />
    <ClInclude Include="lvgl\src\misc\lv_printf.h" />
    <ClInclude Include="lvgl\src\misc\lv_style.h" />
    <ClInclude Include="lvgl\src\misc\lv_style_gen.h" />
    <ClInclude Include="lvgl\src\misc\lv_templ.h" />
    <ClInclude Include="lvgl\src\misc\lv_timer.h" />
    <ClInclude Include="lvgl\src\misc\lv_tlsf.h" />
    <ClInclude Include="lvgl\src\misc\lv_txt.h" />
    <ClInclude Include="lvgl\src\misc\lv_txt_ap.h" />
    <ClInclude Include="lvgl\src\misc\lv_types.h" />
    <ClInclude Include="lvgl\src\misc\lv_utils.h" />
    <ClInclude Include="lvgl\src\widgets\lv_arc.h" />
    <ClInclude Include="lvgl\src\widgets\lv_bar.h" />
    <ClInclude Include="lvgl\src\widgets\lv_btn.h" />
    <ClInclude Include="lvgl\src\widgets\lv_btnmatrix.h" />
    <ClInclude Include="lvgl\src\widgets\lv_canvas.h" />
    <ClInclude Include="lvgl\src\widgets\lv_checkbox.h" />
    <ClInclude Include="lvgl\src\widgets\lv_dropdown.h" />
    <ClInclude Include="lvgl\src\widgets\lv_img.h" />
    <ClInclude Include="lvgl\src\widgets\lv_label.h" />
    <ClInclude Include="lvgl\src\widgets\lv_line.h" />
    <ClInclude Include="lvgl\src\widgets\lv_objx_templ.h" />
    <ClInclude Include="lvgl\src\widgets\lv_roller.h" />
    <ClInclude Include="lvgl\src\widgets\lv_slider.h" />
    <ClInclude Include="lvgl\src\widgets\lv_switch.h" />
    <ClInclude Include="lvgl\src\widgets\lv_table.h" />
    <ClInclude Include="lvgl\src\widgets\lv_textarea.h" />
    <ClInclude Include="lvgl\src\lvgl.h" />
    <ClInclude Include="lvgl\src\lv_api_map.h" />
    <ClInclude Include="lvgl\src\lv_conf_internal.h" />
    <ClInclude Include="lvgl\src\lv_conf_kconfig.h" />
    <ClInclude Include="lvgl\lvgl.h" />
    <ClInclude Include="lvgl\lv_conf_template.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\USER\App\App.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DataProc.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_Clock.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_GPS.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_IMU.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_MAG.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_MusicPlayer.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_Power.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_Recorder.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_SportStatus.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_Storage.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_SysConfig.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_TrackFilter.cpp" />
    <ClCompile Include="..\..\USER\App\Common\DataProc\DP_TzConv.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\AppFactory.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Dialplate\Dialplate.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Dialplate\DialplateModel.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Dialplate\DialplateView.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\LiveMap\LiveMap.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\LiveMap\LiveMapModel.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\LiveMap\LiveMapView.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Startup\Startup.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Startup\StartupModel.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\Startup\StartupView.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\StatusBar\StatusBar.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\SystemInfos\SystemInfos.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\SystemInfos\SystemInfosModel.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\SystemInfos\SystemInfosView.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\_Template\Template.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\_Template\TemplateModel.cpp" />
    <ClCompile Include="..\..\USER\App\Pages\_Template\TemplateView.cpp" />
    <ClCompile Include="..\..\USER\App\Resource\Font\font_agencyb_36.c" />
    <ClCompile Include="..\..\USER\App\Resource\Font\font_bahnschrift_13.c" />
    <ClCompile Include="..\..\USER\App\Resource\Font\font_bahnschrift_17.c" />
    <ClCompile Include="..\..\USER\App\Resource\Font\font_bahnschrift_32.c" />
    <ClCompile Include="..\..\USER\App\Resource\Font\font_bahnschrift_65.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_alarm.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_battery.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_battery_info.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_bicycle.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_compass.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_gps_arrow_dark.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_gps_arrow_default.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_gps_arrow_light.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_gyroscope.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_locate.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_map_location.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_menu.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_origin_point.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_pause.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_satellite.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_sd_card.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_start.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_stop.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_storage.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_system_info.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_time_info.c" />
    <ClCompile Include="..\..\USER\App\Resource\Image\img_src_trip.c" />
    <ClCompile Include="..\..\USER\App\Resource\ResourcePool.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\DataCenter\Account.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\DataCenter\DataCenter.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\DataCenter\PingPongBuffer\PingPongBuffer.c" />
    <ClCompile Include="..\..\USER\App\Utils\GPX\GPX.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\GPX_Parser\GPX_Parser.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\lv_anim_label\lv_anim_label.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_ext\lv_anim_timeline_wrapper.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_ext\lv_obj_ext_func.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\lv_img_png.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\adler32.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\crc32.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\infback.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inffast.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inflate.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\inftrees.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\PNGdec.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\zutil.c" />
    <ClCompile Include="..\..\USER\App\Utils\lv_poly_line\lv_poly_line.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\MapConv\GPS_Transform\GPS_Transform.c" />
    <ClCompile Include="..\..\USER\App\Utils\MapConv\MapConv.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\MapConv\TileSystem\TileSystem.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\new\new.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PageBase.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PM_Base.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PM_Drag.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PM_Router.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PM_State.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PageManager\PM_Anim.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\PointContainer\PointContainer.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\ResourceManager\ResourceManager.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\StorageService\StorageService.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\Stream\Print.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\Stream\Stream.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\TileConv\TileConv.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\Time\DateStrings.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\Time\Time.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\TonePlayer\TonePlayer.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\TrackFilter\TrackLineFilter.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\TrackFilter\TrackPointFilter.cpp" />
    <ClCompile Include="..\..\USER\App\Utils\WString\dtostrf.c" />
    <ClCompile Include="..\..\USER\App\Utils\WString\WString.cpp" />
    <ClCompile Include="HAL\HAL.cpp" />
    <ClCompile Include="HAL\HAL_Audio.cpp" />
    <ClCompile Include="HAL\HAL_Buzz.cpp" />
    <ClCompile Include="HAL\HAL_Clock.cpp" />
    <ClCompile Include="HAL\HAL_Encoder.cpp" />
    <ClCompile Include="HAL\HAL_GPS.cpp" />
    <ClCompile Include="HAL\HAL_IMU.cpp" />
    <ClCompile Include="HAL\HAL_MAG.cpp" />
    <ClCompile Include="HAL\HAL_Power.cpp" />
    <ClCompile Include="HAL\HAL_SD_CARD.cpp" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_1.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_2.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_3.c" />
    <ClCompile Include="lvgl\examples\anim\lv_example_anim_timeline_1.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_left.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_mid.c" />
    <ClCompile Include="lvgl\examples\assets\imgbtn_right.c" />
    <ClCompile Include="lvgl\examples\assets\img_caret_down.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_alpha16.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_argb.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_chroma_keyed.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_indexed16.c" />
    <ClCompile Include="lvgl\examples\assets\img_cogwheel_rgb.c" />
    <ClCompile Include="lvgl\examples\assets\img_hand.c" />
    <ClCompile Include="lvgl\examples\assets\img_skew_strip.c" />
    <ClCompile Include="lvgl\examples\assets\img_star.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_1.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_2.c" />
    <ClCompile Include="lvgl\examples\get_started\lv_example_get_started_3.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_1.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_2.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_3.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_4.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_5.c" />
    <ClCompile Include="lvgl\examples\layouts\flex\lv_example_flex_6.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_1.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_2.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_3.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_4.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_5.c" />
    <ClCompile Include="lvgl\examples\layouts\grid\lv_example_grid_6.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_disp_template.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_fs_template.c" />
    <ClCompile Include="lvgl\examples\porting\lv_port_indev_template.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_1.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_2.c" />
    <ClCompile Include="lvgl\examples\scroll\lv_example_scroll_3.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_1.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_10.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_11.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_2.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_3.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_4.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_6.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_7.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_8.c" />
    <ClCompile Include="lvgl\examples\styles\lv_example_style_9.c" />
    <ClCompile Include="lvgl\examples\widgets\arc\lv_example_arc_1.c" />
    <ClCompile Include="lvgl\examples\widgets\arc\lv_example_arc_2.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_1.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_2.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_3.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_4.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_5.c" />
    <ClCompile Include="lvgl\examples\widgets\bar\lv_example_bar_6.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_1.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_2.c" />
    <ClCompile Include="lvgl\examples\widgets\btn\lv_example_btn_3.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_1.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_2.c" />
    <ClCompile Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_3.c" />
    <ClCompile Include="lvgl\examples\widgets\calendar\lv_example_calendar_1.c" />
    <ClCompile Include="lvgl\examples\widgets\canvas\lv_example_canvas_1.c" />
    <ClCompile Include="lvgl\examples\widgets\canvas\lv_example_canvas_2.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_1.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_2.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_3.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_4.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_5.c" />
    <ClCompile Include="lvgl\examples\widgets\chart\lv_example_chart_6.c" />
    <ClCompile Include="lvgl\examples\widgets\checkbox\lv_example_checkbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\colorwheel\lv_example_colorwheel_1.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_1.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_2.c" />
    <ClCompile Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_3.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_1.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_2.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_3.c" />
    <ClCompile Include="lvgl\examples\widgets\img\lv_example_img_4.c" />
    <ClCompile Include="lvgl\examples\widgets\imgbtn\lv_example_imgbtn_1.c" />
    <ClCompile Include="lvgl\examples\widgets\keyboard\lv_example_keyboard_1.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_1.c" />
    <ClCompile Include="lvgl\examples\widgets\label\lv_example_label_2.c" />
    <ClCompile Include="lvgl\examples\widgets\led\lv_example_led_1.c" />
    <ClCompile Include="lvgl\examples\widgets\line\lv_example_line_1.c" />
    <ClCompile Include="lvgl\examples\widgets\list\lv_example_list_1.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_1.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_2.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_3.c" />
    <ClCompile Include="lvgl\examples\widgets\meter\lv_example_meter_4.c" />
    <ClCompile Include="lvgl\examples\widgets\msgbox\lv_example_msgbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\roller\lv_example_roller_1.c" />
    <ClCompile Include="lvgl\examples\widgets\roller\lv_example_roller_2.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_1.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_2.c" />
    <ClCompile Include="lvgl\examples\widgets\slider\lv_example_slider_3.c" />
    <ClCompile Include="lvgl\examples\widgets\spinbox\lv_example_spinbox_1.c" />
    <ClCompile Include="lvgl\examples\widgets\spinner\lv_example_spinner_1.c" />
    <ClCompile Include="lvgl\examples\widgets\switch\lv_example_switch_1.c" />
    <ClCompile Include="lvgl\examples\widgets\table\lv_example_table_1.c" />
    <ClCompile Include="lvgl\examples\widgets\table\lv_example_table_2.c" />
    <ClCompile Include="lvgl\examples\widgets\tabview\lv_example_tabview_1.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_1.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_2.c" />
    <ClCompile Include="lvgl\examples\widgets\textarea\lv_example_textarea_3.c" />
    <ClCompile Include="lvgl\examples\widgets\tileview\lv_example_tileview_1.c" />
    <ClCompile Include="lvgl\examples\widgets\win\lv_example_win_1.c" />
    <ClCompile Include="lvgl\src\core\lv_disp.c" />
    <ClCompile Include="lvgl\src\core\lv_event.c" />
    <ClCompile Include="lvgl\src\core\lv_group.c" />
    <ClCompile Include="lvgl\src\core\lv_indev.c" />
    <ClCompile Include="lvgl\src\core\lv_indev_scroll.c" />
    <ClCompile Include="lvgl\src\core\lv_obj.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_class.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_draw.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_pos.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_scroll.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_style.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_style_gen.c" />
    <ClCompile Include="lvgl\src\core\lv_obj_tree.c" />
    <ClCompile Include="lvgl\src\core\lv_refr.c" />
    <ClCompile Include="lvgl\src\core\lv_theme.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_arc.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_img.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_label.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_line.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_mask.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_rect.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_transform.c" />
    <ClCompile Include="lvgl\src\draw\lv_draw_triangle.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_buf.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_cache.c" />
    <ClCompile Include="lvgl\src\draw\lv_img_decoder.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_arc.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_blend.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_gradient.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_img.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_letter.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_line.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_polygon.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_rect.c" />
    <ClCompile Include="lvgl\src\draw\sw\lv_draw_sw_transform.c" />
    <ClCompile Include="lvgl\src\extra\layouts\flex\lv_flex.c" />
    <ClCompile Include="lvgl\src\extra\layouts\grid\lv_grid.c" />
    <ClCompile Include="lvgl\src\extra\libs\png\lodepng.c" />
    <ClCompile Include="lvgl\src\extra\libs\png\lv_png.c" />
    <ClCompile Include="lvgl\src\extra\others\monkey\lv_monkey.c" />
    <ClCompile Include="lvgl\src\extra\others\snapshot\lv_snapshot.c" />
    <ClCompile Include="lvgl\src\extra\themes\basic\lv_theme_basic.c" />
    <ClCompile Include="lvgl\src\extra\themes\default\lv_theme_default.c" />
    <ClCompile Include="lvgl\src\extra\themes\mono\lv_theme_mono.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_arrow.c" />
    <ClCompile Include="lvgl\src\extra\widgets\calendar\lv_calendar_header_dropdown.c" />
    <ClCompile Include="lvgl\src\extra\widgets\chart\lv_chart.c" />
    <ClCompile Include="lvgl\src\extra\widgets\colorwheel\lv_colorwheel.c" />
    <ClCompile Include="lvgl\src\extra\widgets\imgbtn\lv_imgbtn.c" />
    <ClCompile Include="lvgl\src\extra\widgets\keyboard\lv_keyboard.c" />
    <ClCompile Include="lvgl\src\extra\widgets\led\lv_led.c" />
    <ClCompile Include="lvgl\src\extra\widgets\list\lv_list.c" />
    <ClCompile Include="lvgl\src\extra\widgets\menu\lv_menu.c" />
    <ClCompile Include="lvgl\src\extra\widgets\meter\lv_meter.c" />
    <ClCompile Include="lvgl\src\extra\widgets\msgbox\lv_msgbox.c" />
    <ClCompile Include="lvgl\src\extra\widgets\span\lv_span.c" />
    <ClCompile Include="lvgl\src\extra\widgets\spinbox\lv_spinbox.c" />
    <ClCompile Include="lvgl\src\extra\widgets\spinner\lv_spinner.c" />
    <ClCompile Include="lvgl\src\extra\widgets\tabview\lv_tabview.c" />
    <ClCompile Include="lvgl\src\extra\widgets\tileview\lv_tileview.c" />
    <ClCompile Include="lvgl\src\extra\widgets\win\lv_win.c" />
    <ClCompile Include="lvgl\src\extra\lv_extra.c" />
    <ClCompile Include="lvgl\src\font\lv_font.c" />
    <ClCompile Include="lvgl\src\font\lv_font_dejavu_16_persian_hebrew.c" />
    <ClCompile Include="lvgl\src\font\lv_font_fmt_txt.c" />
    <ClCompile Include="lvgl\src\font\lv_font_loader.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_10.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_12.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_12_subpx.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_14.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_16.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_18.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_20.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_22.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_24.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_26.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_28.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_28_compressed.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_30.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_32.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_34.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_36.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_38.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_40.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_42.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_44.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_46.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_48.c" />
    <ClCompile Include="lvgl\src\font\lv_font_montserrat_8.c" />
    <ClCompile Include="lvgl\src\font\lv_font_simsun_16_cjk.c" />
    <ClCompile Include="lvgl\src\font\lv_font_unscii_16.c" />
    <ClCompile Include="lvgl\src\font\lv_font_unscii_8.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_disp.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_indev.c" />
    <ClCompile Include="lvgl\src\hal\lv_hal_tick.c" />
    <ClCompile Include="lvgl\src\misc\lv_anim.c" />
    <ClCompile Include="lvgl\src\misc\lv_anim_timeline.c" />
    <ClCompile Include="lvgl\src\misc\lv_area.c" />
    <ClCompile Include="lvgl\src\misc\lv_async.c" />
    <ClCompile Include="lvgl\src\misc\lv_bidi.c" />
    <ClCompile Include="lvgl\src\misc\lv_color.c" />
    <ClCompile Include="lvgl\src\misc\lv_fs.c" />
    <ClCompile Include="lvgl\src\misc\lv_gc.c" />
    <ClCompile Include="lvgl\src\misc\lv_ll.c" />
    <ClCompile Include="lvgl\src\misc\lv_log.c" />
    <ClCompile Include="lvgl\src\misc\lv_math.c" />
    <ClCompile Include="lvgl\src\misc\lv_mem.c" />
    <ClCompile Include="lvgl\src\misc\lv_printf.c" />
    <ClCompile Include="lvgl\src\misc\lv_style.c" />
    <ClCompile Include="lvgl\src\misc\lv_style_gen.c" />
    <ClCompile Include="lvgl\src\misc\lv_templ.c" />
    <ClCompile Include="lvgl\src\misc\lv_timer.c" />
    <ClCompile Include="lvgl\src\misc\lv_tlsf.c" />
    <ClCompile Include="lvgl\src\misc\lv_txt.c" />
    <ClCompile Include="lvgl\src\misc\lv_txt_ap.c" />
    <ClCompile Include="lvgl\src\misc\lv_utils.c" />
    <ClCompile Include="lvgl\src\widgets\lv_arc.c" />
    <ClCompile Include="lvgl\src\widgets\lv_bar.c" />
    <ClCompile Include="lvgl\src\widgets\lv_btn.c" />
    <ClCompile Include="lvgl\src\widgets\lv_btnmatrix.c" />
    <ClCompile Include="lvgl\src\widgets\lv_canvas.c" />
    <ClCompile Include="lvgl\src\widgets\lv_checkbox.c" />
    <ClCompile Include="lvgl\src\widgets\lv_dropdown.c" />
    <ClCompile Include="lvgl\src\widgets\lv_img.c" />
    <ClCompile Include="lvgl\src\widgets\lv_label.c" />
    <ClCompile Include="lvgl\src\widgets\lv_line.c" />
    <ClCompile Include="lvgl\src\widgets\lv_objx_templ.c" />
    <ClCompile Include="lvgl\src\widgets\lv_roller.c" />
    <ClCompile Include="lvgl\src\widgets\lv_slider.c" />
    <ClCompile Include="lvgl\src\widgets\lv_switch.c" />
    <ClCompile Include="lvgl\src\widgets\lv_table.c" />
    <ClCompile Include="lvgl\src\widgets\lv_textarea.c" />
    <ClCompile Include="lv_fs_if\lv_fs_fatfs.c" />
    <ClCompile Include="lv_fs_if\lv_fs_if.c" />
    <ClCompile Include="lv_fs_if\lv_fs_pc.c" />
    <ClCompile Include="lv_fs_if\lv_fs_posix.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\USER\App\Common\DataProc\DP_LIST.inc" />
    <None Include="..\..\USER\App\Common\Music\MusicCode.h" />
    <None Include="..\..\USER\App\Utils\lv_img_png\PNGdec\src\png.inl" />
    <None Include="lvgl\.github\ISSUE_TEMPLATE\bug-report.md" />
    <None Include="lvgl\.github\ISSUE_TEMPLATE\config.yml" />
    <None Include="lvgl\.github\ISSUE_TEMPLATE\dev-discussion.md" />
    <None Include="lvgl\.github\workflows\build_micropython.yml" />
    <None Include="lvgl\.github\workflows\ccpp.yml" />
    <None Include="lvgl\.github\workflows\main.yml" />
    <None Include="lvgl\.github\workflows\release.yml" />
    <None Include="lvgl\.github\auto-comment.yml" />
    <None Include="lvgl\.github\FUNDING.yml" />
    <None Include="lvgl\.github\pull_request_template.md" />
    <None Include="lvgl\.github\stale.yml" />
    <None Include="lvgl\docs\get-started\arduino.md" />
    <None Include="lvgl\docs\get-started\espressif.md" />
    <None Include="lvgl\docs\get-started\index.md" />
    <None Include="lvgl\docs\get-started\micropython.md" />
    <None Include="lvgl\docs\get-started\nuttx.md" />
    <None Include="lvgl\docs\get-started\nxp.md" />
    <None Include="lvgl\docs\get-started\pc-simulator.md" />
    <None Include="lvgl\docs\get-started\quick-overview.md" />
    <None Include="lvgl\docs\get-started\stm32.md" />
    <None Include="lvgl\docs\intro\index.md" />
    <None Include="lvgl\docs\layouts\flex.md" />
    <None Include="lvgl\docs\layouts\grid.md" />
    <None Include="lvgl\docs\layouts\index.md" />
    <None Include="lvgl\docs\misc\align.png" />
    <None Include="lvgl\docs\misc\bidi.png" />
    <None Include="lvgl\docs\misc\button_style_example.gif" />
    <None Include="lvgl\docs\misc\button_style_example.png" />
    <None Include="lvgl\docs\misc\codeblocks.jpg" />
    <None Include="lvgl\docs\misc\eclipse.jpg" />
    <None Include="lvgl\docs\misc\layers.png" />
    <None Include="lvgl\docs\misc\list_theme_example.gif" />
    <None Include="lvgl\docs\misc\list_theme_example.png" />
    <None Include="lvgl\docs\misc\lv_theme_intro.png" />
    <None Include="lvgl\docs\misc\par_child1.png" />
    <None Include="lvgl\docs\misc\par_child2.png" />
    <None Include="lvgl\docs\misc\par_child3.png" />
    <None Include="lvgl\docs\misc\platformio.jpg" />
    <None Include="lvgl\docs\misc\qtcreator.jpg" />
    <None Include="lvgl\docs\misc\simple_button_example.gif" />
    <None Include="lvgl\docs\misc\simple_button_example.png" />
    <None Include="lvgl\docs\misc\slider_example.gif" />
    <None Include="lvgl\docs\misc\slider_example.png" />
    <None Include="lvgl\docs\misc\style-built-in.png" />
    <None Include="lvgl\docs\misc\style-example.png" />
    <None Include="lvgl\docs\misc\symbols.png" />
    <None Include="lvgl\docs\misc\sys.png" />
    <None Include="lvgl\docs\misc\theme-example.png" />
    <None Include="lvgl\docs\misc\visualstudio.jpg" />
    <None Include="lvgl\docs\overview\animation.md" />
    <None Include="lvgl\docs\overview\coords.md" />
    <None Include="lvgl\docs\overview\display.md" />
    <None Include="lvgl\docs\overview\drawing.md" />
    <None Include="lvgl\docs\overview\event.md" />
    <None Include="lvgl\docs\overview\file-system.md" />
    <None Include="lvgl\docs\overview\font.md" />
    <None Include="lvgl\docs\overview\image.md" />
    <None Include="lvgl\docs\overview\indev.md" />
    <None Include="lvgl\docs\overview\index.md" />
    <None Include="lvgl\docs\overview\layer.md" />
    <None Include="lvgl\docs\overview\new_widget.md" />
    <None Include="lvgl\docs\overview\object.md" />
    <None Include="lvgl\docs\overview\scroll.md" />
    <None Include="lvgl\docs\overview\style.md" />
    <None Include="lvgl\docs\overview\timer.md" />
    <None Include="lvgl\docs\porting\display.md" />
    <None Include="lvgl\docs\porting\indev.md" />
    <None Include="lvgl\docs\porting\index.md" />
    <None Include="lvgl\docs\porting\log.md" />
    <None Include="lvgl\docs\porting\os.md" />
    <None Include="lvgl\docs\porting\project.md" />
    <None Include="lvgl\docs\porting\sleep.md" />
    <None Include="lvgl\docs\porting\sys.md" />
    <None Include="lvgl\docs\porting\task-handler.md" />
    <None Include="lvgl\docs\porting\tick.md" />
    <None Include="lvgl\docs\widgets\arc.md" />
    <None Include="lvgl\docs\widgets\bar.md" />
    <None Include="lvgl\docs\widgets\btn.md" />
    <None Include="lvgl\docs\widgets\btnmatrix.md" />
    <None Include="lvgl\docs\widgets\calendar.md" />
    <None Include="lvgl\docs\widgets\canvas.md" />
    <None Include="lvgl\docs\widgets\chart.md" />
    <None Include="lvgl\docs\widgets\checkbox.md" />
    <None Include="lvgl\docs\widgets\cont.md" />
    <None Include="lvgl\docs\widgets\cpicker.md" />
    <None Include="lvgl\docs\widgets\dropdown.md" />
    <None Include="lvgl\docs\widgets\gauge.md" />
    <None Include="lvgl\docs\widgets\img.md" />
    <None Include="lvgl\docs\widgets\imgbtn.md" />
    <None Include="lvgl\docs\widgets\index.md" />
    <None Include="lvgl\docs\widgets\keyboard.md" />
    <None Include="lvgl\docs\widgets\label.md" />
    <None Include="lvgl\docs\widgets\led.md" />
    <None Include="lvgl\docs\widgets\line.md" />
    <None Include="lvgl\docs\widgets\linemeter.md" />
    <None Include="lvgl\docs\widgets\list.md" />
    <None Include="lvgl\docs\widgets\msgbox.md" />
    <None Include="lvgl\docs\widgets\obj.md" />
    <None Include="lvgl\docs\widgets\objmask.md" />
    <None Include="lvgl\docs\widgets\page.md" />
    <None Include="lvgl\docs\widgets\roller.md" />
    <None Include="lvgl\docs\widgets\slider.md" />
    <None Include="lvgl\docs\widgets\spinbox.md" />
    <None Include="lvgl\docs\widgets\spinner.md" />
    <None Include="lvgl\docs\widgets\switch.md" />
    <None Include="lvgl\docs\widgets\table.md" />
    <None Include="lvgl\docs\widgets\tabview.md" />
    <None Include="lvgl\docs\widgets\textarea.md" />
    <None Include="lvgl\docs\widgets\tileview.md" />
    <None Include="lvgl\docs\widgets\win.md" />
    <None Include="lvgl\docs\CODE_OF_CONDUCT.md" />
    <None Include="lvgl\docs\CODING_STYLE.md" />
    <None Include="lvgl\docs\CONTRIBUTING.md" />
    <None Include="lvgl\docs\ROADMAP.md" />
    <None Include="lvgl\examples\anim\index.rst" />
    <None Include="lvgl\examples\anim\lv_example_anim_1.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_2.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_3.py" />
    <None Include="lvgl\examples\anim\lv_example_anim_timeline_1.py" />
    <None Include="lvgl\examples\arduino\LVGL_Arduino.ino" />
    <None Include="lvgl\examples\assets\caret_down.png" />
    <None Include="lvgl\examples\assets\imgbtn_left.png" />
    <None Include="lvgl\examples\assets\imgbtn_mid.png" />
    <None Include="lvgl\examples\assets\imgbtn_right.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_argb.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_chroma_keyed.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_indexed16.png" />
    <None Include="lvgl\examples\assets\img_cogwheel_rgb.png" />
    <None Include="lvgl\examples\assets\img_hand_min.png" />
    <None Include="lvgl\examples\assets\skew_strip.png" />
    <None Include="lvgl\examples\assets\star.png" />
    <None Include="lvgl\examples\widgets\arc\index.rst" />
    <None Include="lvgl\examples\widgets\arc\lv_example_arc_1.py" />
    <None Include="lvgl\examples\widgets\arc\lv_example_arc_2.py" />
    <None Include="lvgl\examples\widgets\bar\index.rst" />
    <None Include="lvgl\examples\widgets\bar\lv_example_bar_1.py" />
    <None Include="lvgl\examples\widgets\btn\index.rst" />
    <None Include="lvgl\examples\widgets\btn\lv_example_btn_1.py" />
    <None Include="lvgl\examples\widgets\btnmatrix\index.rst" />
    <None Include="lvgl\examples\widgets\btnmatrix\lv_example_btnmatrix_1.py" />
    <None Include="lvgl\examples\widgets\canvas\index.rst" />
    <None Include="lvgl\examples\widgets\canvas\lv_example_canvas_1.py" />
    <None Include="lvgl\examples\widgets\canvas\lv_example_canvas_2.py" />
    <None Include="lvgl\examples\widgets\chart\index.rst" />
    <None Include="lvgl\examples\widgets\chart\lv_example_chart_1.py" />
    <None Include="lvgl\examples\widgets\checkbox\index.rst" />
    <None Include="lvgl\examples\widgets\checkbox\lv_example_checkbox_1.py" />
    <None Include="lvgl\examples\widgets\dropdown\index.rst" />
    <None Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_1.py" />
    <None Include="lvgl\examples\widgets\dropdown\lv_example_dropdown_2.py" />
    <None Include="lvgl\examples\widgets\img\index.rst" />
    <None Include="lvgl\examples\widgets\img\lv_example_img_1.py" />
    <None Include="lvgl\examples\widgets\imgbtn\index.rst" />
    <None Include="lvgl\examples\widgets\keyboard\index.rst" />
    <None Include="lvgl\examples\widgets\keyboard\lv_example_keyboard_1.py" />
    <None Include="lvgl\examples\widgets\label\index.rst" />
    <None Include="lvgl\examples\widgets\label\lv_example_label_1.py" />
    <None Include="lvgl\examples\widgets\label\lv_example_label_2.py" />
    <None Include="lvgl\examples\widgets\led\index.rst" />
    <None Include="lvgl\examples\widgets\led\lv_example_led_1.py" />
    <None Include="lvgl\examples\widgets\line\index.rst" />
    <None Include="lvgl\examples\widgets\line\lv_example_line_1.py" />
    <None Include="lvgl\examples\widgets\list\index.rst" />
    <None Include="lvgl\examples\widgets\list\lv_example_list_1.py" />
    <None Include="lvgl\examples\widgets\meter\index.rst" />
    <None Include="lvgl\examples\widgets\msgbox\index.rst" />
    <None Include="lvgl\examples\widgets\msgbox\lv_example_msgbox_1.py" />
    <None Include="lvgl\examples\widgets\msgbox\lv_example_msgbox_2.py" />
    <None Include="lvgl\examples\widgets\roller\index.rst" />
    <None Include="lvgl\examples\widgets\roller\lv_example_roller_1.py" />
    <None Include="lvgl\examples\widgets\slider\index.rst" />
    <None Include="lvgl\examples\widgets\slider\lv_example_slider_1.py" />
    <None Include="lvgl\examples\widgets\slider\lv_example_slider_2.py" />
    <None Include="lvgl\examples\widgets\spinbox\index.rst" />
    <None Include="lvgl\examples\widgets\spinbox\lv_example_spinbox_1.py" />
    <None Include="lvgl\examples\widgets\spinner\index.rst" />
    <None Include="lvgl\examples\widgets\spinner\lv_example_spinner_1.py" />
    <None Include="lvgl\examples\widgets\switch\index.rst" />
    <None Include="lvgl\examples\widgets\switch\lv_example_switch_1.py" />
    <None Include="lvgl\examples\widgets\table\index.rst" />
    <None Include="lvgl\examples\widgets\table\lv_example_table_1.py" />
    <None Include="lvgl\examples\widgets\tabview\index.rst" />
    <None Include="lvgl\examples\widgets\tabview\lv_example_tabview_1.py" />
    <None Include="lvgl\examples\widgets\textarea\index.rst" />
    <None Include="lvgl\examples\widgets\textarea\lv_example_textarea_1.py" />
    <None Include="lvgl\examples\widgets\textarea\lv_example_textarea_2.py" />
    <None Include="lvgl\examples\widgets\tileview\index.rst" />
    <None Include="lvgl\examples\widgets\tileview\lv_example_tileview_1.py" />
    <None Include="lvgl\examples\examples.mk" />
    <None Include="lvgl\scripts\built_in_font\built_in_font_gen.py" />
    <None Include="lvgl\scripts\built_in_font\DejaVuSans.ttf" />
    <None Include="lvgl\scripts\built_in_font\FontAwesome5-Solid+Brands+Regular.woff" />
    <None Include="lvgl\scripts\built_in_font\generate_all.py" />
    <None Include="lvgl\scripts\built_in_font\Montserrat-Medium.ttf" />
    <None Include="lvgl\scripts\built_in_font\SimSun.woff" />
    <None Include="lvgl\scripts\built_in_font\unscii-8.ttf" />
    <None Include="lvgl\scripts\release\com.py" />
    <None Include="lvgl\scripts\release\dev.py" />
    <None Include="lvgl\scripts\release\main.py" />
    <None Include="lvgl\scripts\release\proj.py" />
    <None Include="lvgl\scripts\release\release.py" />
    <None Include="lvgl\scripts\code-format.cfg" />
    <None Include="lvgl\scripts\code-format.sh" />
    <None Include="lvgl\scripts\cppcheck_run.sh" />
    <None Include="lvgl\scripts\Doxyfile" />
    <None Include="lvgl\scripts\infer_run.sh" />
    <None Include="lvgl\scripts\lv_conf_checker.py" />
    <None Include="lvgl\scripts\style_api_gen.py" />
    <None Include="lvgl\src\core\lv_core.mk" />
    <None Include="lvgl\src\draw\lv_draw.mk" />
    <None Include="lvgl\src\extra\extra.mk" />
    <None Include="lvgl\src\font\lv_font.mk" />
    <None Include="lvgl\src\hal\lv_hal.mk" />
    <None Include="lvgl\src\misc\lv_misc.mk" />
    <None Include="lvgl\src\widgets\lv_widgets.mk" />
    <None Include="lvgl\zephyr\module.yml" />
    <None Include="lvgl\.editorconfig" />
    <None Include="lvgl\.git" />
    <None Include="lvgl\.gitignore" />
    <None Include="lvgl\.gitmodules" />
    <None Include="lvgl\CHANGELOG.md" />
    <None Include="lvgl\CMakeLists.txt" />
    <None Include="lvgl\component.mk" />
    <None Include="lvgl\Kconfig" />
    <None Include="lvgl\library.json" />
    <None Include="lvgl\library.properties" />
    <None Include="lvgl\LICENCE.txt" />
    <None Include="lvgl\lvgl.mk" />
    <None Include="lvgl\README.md" />
    <None Include="lv_fs_if\.gitignore" />
    <None Include="lv_fs_if\LICENSE" />
    <None Include="lv_fs_if\README.md" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="lv_drivers\gtkdrv\gtkdrv.h" />
    <ClInclude Include="lv_drivers\wayland\wayland.h" />
    <ClInclude Include="lv_drivers\win32drv\win32drv.h" />
    <ClInclude Include="lv_drivers\lv_drv_conf_template.h" />
    <ClInclude Include="lv_drivers\win_drv.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="lv_drivers\gtkdrv\gtkdrv.c" />
    <ClCompile Include="lv_drivers\wayland\wayland.c" />
    <ClCompile Include="lv_drivers\win32drv\win32drv.c" />
    <ClCompile Include="lv_drivers\win_drv.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="lv_drivers\.github\auto-comment.yml" />
    <None Include="lv_drivers\.github\stale.yml" />
    <None Include="lv_drivers\docs\astyle_c" />
    <None Include="lv_drivers\docs\astyle_h" />
    <None Include="lv_drivers\gtkdrv\broadway.png" />
    <None Include="lv_drivers\gtkdrv\README.md" />
    <None Include="lv_drivers\wayland\README.md" />
    <None Include="lv_drivers\.git" />
    <None Include="lv_drivers\.gitignore" />
    <None Include="lv_drivers\CMakeLists.txt" />
    <None Include="lv_drivers\library.json" />
    <None Include="lv_drivers\LICENSE" />
    <None Include="lv_drivers\lv_drivers.mk" />
    <None Include="lv_drivers\README.md" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="lv_conf.h" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="lv_drv_conf.h" />
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="LVGL.Simulator.manifest" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="lv_fs_if\dirent.h" />
    <ClInclude Include="lv_fs_if\lv_fs_if.h" />
    <ClInclude Include="Mile.Project.Properties.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="LVGL.Simulator.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="LVGL.Simulator.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="LVGL.ico" />
  </ItemGroup>
  <Import Project="Mile.Project\Mile.Project.Cpp.targets" />
</Project>
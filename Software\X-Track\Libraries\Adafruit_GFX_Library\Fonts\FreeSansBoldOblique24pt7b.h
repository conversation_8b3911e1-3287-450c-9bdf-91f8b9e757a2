const uint8_t FreeSansBoldOblique24pt7bBitmaps[] PROGMEM = {
  0x01, 0xE0, 0x07, 0xF0, 0x1F, 0xC0, 0xFF, 0x03, 0xF8, 0x0F, 0xE0, 0x3F,
  0x80, 0xFE, 0x07, 0xF0, 0x1F, 0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xE0, 0x1F,
  0x80, 0x7E, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0xF8, 0x03, 0xE0, 0x0F,
  0x80, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x1F,
  0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0xFE, 0x03, 0xF8, 0x00,
  0x7E, 0x0F, 0xDF, 0x83, 0xF7, 0xE0, 0xFF, 0xF0, 0x7E, 0xFC, 0x1F, 0xBF,
  0x07, 0xEF, 0xC1, 0xFB, 0xE0, 0x7C, 0xF8, 0x1F, 0x3C, 0x07, 0x8F, 0x01,
  0xE3, 0x80, 0x70, 0x00, 0x07, 0xC1, 0xF0, 0x00, 0x3E, 0x0F, 0x80, 0x03,
  0xE0, 0xF8, 0x00, 0x1F, 0x07, 0xC0, 0x01, 0xF0, 0x7C, 0x00, 0x0F, 0x83,
  0xE0, 0x00, 0xF8, 0x3E, 0x00, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xF8,
  0x7F, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xE0, 0x0F,
  0x83, 0xE0, 0x00, 0x7C, 0x3E, 0x00, 0x07, 0xC1, 0xF0, 0x00, 0x3E, 0x0F,
  0x80, 0x03, 0xE0, 0xF8, 0x00, 0x1F, 0x07, 0xC0, 0x00, 0xF8, 0x7C, 0x00,
  0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFF, 0x83, 0xFF,
  0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0xE0, 0x1F, 0x07, 0xC0, 0x00, 0xF8, 0x3E,
  0x00, 0x0F, 0x83, 0xE0, 0x00, 0x7C, 0x1F, 0x00, 0x07, 0xC1, 0xF0, 0x00,
  0x3E, 0x0F, 0x80, 0x01, 0xF0, 0xF8, 0x00, 0x1F, 0x07, 0xC0, 0x00, 0xF8,
  0x3C, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x38, 0x00, 0x00, 0x0E,
  0x00, 0x00, 0x3F, 0xF0, 0x00, 0x7F, 0xFF, 0x00, 0x3F, 0xFF, 0xE0, 0x1F,
  0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0x07, 0xF3, 0x9F, 0xC1, 0xF8, 0xE3, 0xF0,
  0x7C, 0x38, 0xFC, 0x3F, 0x0E, 0x3F, 0x0F, 0xC7, 0x8F, 0xC3, 0xF1, 0xC0,
  0x00, 0xFE, 0x70, 0x00, 0x3F, 0xDC, 0x00, 0x0F, 0xFF, 0x00, 0x01, 0xFF,
  0xE0, 0x00, 0x3F, 0xFE, 0x00, 0x0F, 0xFF, 0xE0, 0x00, 0xFF, 0xFC, 0x00,
  0x0F, 0xFF, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x77, 0xF8, 0x00, 0x1C, 0xFE,
  0x00, 0x07, 0x3F, 0x8F, 0xE3, 0xCF, 0xE3, 0xF8, 0xE3, 0xF8, 0xFE, 0x38,
  0xFC, 0x3F, 0x8E, 0x7F, 0x0F, 0xF3, 0x9F, 0xC3, 0xFD, 0xFF, 0xE0, 0x7F,
  0xFF, 0xF0, 0x1F, 0xFF, 0xFC, 0x03, 0xFF, 0xFC, 0x00, 0x7F, 0xFE, 0x00,
  0x03, 0xFC, 0x00, 0x00, 0x38, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x03, 0x80,
  0x00, 0x01, 0xE0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x01, 0xF8, 0x00, 0x07,
  0x80, 0x7F, 0xE0, 0x00, 0xF0, 0x0F, 0xFF, 0x00, 0x1E, 0x01, 0xFF, 0xF0,
  0x01, 0xC0, 0x3F, 0xFF, 0x80, 0x3C, 0x07, 0xE1, 0xF8, 0x07, 0x80, 0x78,
  0x07, 0x80, 0xF0, 0x0F, 0x80, 0x78, 0x0E, 0x00, 0xF0, 0x07, 0x81, 0xC0,
  0x0F, 0x00, 0xF8, 0x3C, 0x00, 0xF0, 0x0F, 0x07, 0x80, 0x0F, 0xC3, 0xF0,
  0xF0, 0x00, 0xFF, 0xFE, 0x0E, 0x00, 0x07, 0xFF, 0xC1, 0xE0, 0x00, 0x7F,
  0xF8, 0x3C, 0x00, 0x03, 0xFF, 0x07, 0x80, 0x00, 0x0F, 0xC0, 0x70, 0x00,
  0x00, 0x00, 0x0E, 0x03, 0xF0, 0x00, 0x01, 0xE0, 0xFF, 0xC0, 0x00, 0x3C,
  0x1F, 0xFE, 0x00, 0x03, 0x83, 0xFF, 0xE0, 0x00, 0x70, 0x7F, 0xFF, 0x00,
  0x0F, 0x0F, 0xC3, 0xF0, 0x01, 0xE0, 0xF0, 0x0F, 0x00, 0x3C, 0x1F, 0x00,
  0xF0, 0x03, 0x81, 0xE0, 0x0F, 0x00, 0x78, 0x1E, 0x01, 0xF0, 0x0F, 0x01,
  0xE0, 0x1E, 0x01, 0xE0, 0x1F, 0x87, 0xE0, 0x1C, 0x01, 0xFF, 0xFC, 0x03,
  0x80, 0x0F, 0xFF, 0x80, 0x78, 0x00, 0xFF, 0xF0, 0x0F, 0x00, 0x07, 0xFE,
  0x01, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x3F, 0xF0,
  0x00, 0x07, 0xFF, 0xC0, 0x00, 0x7F, 0xFF, 0x00, 0x03, 0xFF, 0xF8, 0x00,
  0x3F, 0x9F, 0xC0, 0x03, 0xF8, 0x7E, 0x00, 0x1F, 0xC3, 0xF0, 0x00, 0xFE,
  0x1F, 0x00, 0x07, 0xF1, 0xF8, 0x00, 0x3F, 0xCF, 0xC0, 0x01, 0xFE, 0xFC,
  0x00, 0x07, 0xFF, 0xC0, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0xFF, 0xC0, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x1F, 0xFF, 0x07, 0xC1, 0xFF,
  0xF8, 0x3E, 0x3F, 0xFF, 0xE3, 0xE3, 0xFE, 0x3F, 0x1F, 0x1F, 0xC1, 0xFD,
  0xF1, 0xFC, 0x07, 0xFF, 0x8F, 0xC0, 0x3F, 0xF8, 0xFE, 0x00, 0xFF, 0xC7,
  0xF0, 0x07, 0xFC, 0x3F, 0x80, 0x1F, 0xC1, 0xFC, 0x00, 0xFE, 0x0F, 0xF0,
  0x1F, 0xF8, 0x7F, 0xC1, 0xFF, 0xC1, 0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF,
  0xFC, 0x3F, 0xFF, 0xCF, 0xE0, 0x7F, 0xF8, 0x7F, 0x80, 0xFF, 0x00, 0x00,
  0x7E, 0xFD, 0xFF, 0xEF, 0xDF, 0xBF, 0x7C, 0xF9, 0xE3, 0xC7, 0x00, 0x00,
  0x0F, 0x80, 0x0F, 0x80, 0x0F, 0x80, 0x0F, 0xC0, 0x07, 0xC0, 0x07, 0xC0,
  0x07, 0xE0, 0x03, 0xE0, 0x03, 0xE0, 0x03, 0xF0, 0x01, 0xF0, 0x01, 0xF8,
  0x00, 0xF8, 0x00, 0xFC, 0x00, 0x7C, 0x00, 0x7E, 0x00, 0x3E, 0x00, 0x1F,
  0x00, 0x1F, 0x80, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x03, 0xF0, 0x01,
  0xF0, 0x00, 0xF8, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x1F, 0x00, 0x0F, 0x80,
  0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0xF8, 0x00, 0x7C, 0x00, 0x1E,
  0x00, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
  0x3E, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00,
  0x1E, 0x00, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x00, 0xF0, 0x00, 0x7C,
  0x00, 0x3E, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x03, 0xE0, 0x01, 0xF0, 0x00,
  0xF8, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x1F, 0x00, 0x0F, 0x80, 0x07, 0xC0,
  0x03, 0xE0, 0x01, 0xF0, 0x00, 0xF8, 0x00, 0xF8, 0x00, 0x7C, 0x00, 0x3E,
  0x00, 0x1F, 0x00, 0x1F, 0x80, 0x0F, 0x80, 0x07, 0xC0, 0x07, 0xE0, 0x03,
  0xE0, 0x03, 0xF0, 0x01, 0xF0, 0x01, 0xF8, 0x00, 0xF8, 0x00, 0xFC, 0x00,
  0x7C, 0x00, 0x7C, 0x00, 0x7E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x3F, 0x00,
  0x1F, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0x00, 0x01, 0xE0, 0x03, 0x80, 0x07,
  0x00, 0x0E, 0x07, 0x3C, 0x6F, 0xFF, 0xFF, 0xFF, 0xBF, 0xFE, 0x0F, 0xE0,
  0x1F, 0xC0, 0x7F, 0x81, 0xEF, 0x87, 0x8F, 0x0E, 0x1E, 0x08, 0x10, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00,
  0x7F, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0x7F, 0xFF, 0xFE,
  0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFE, 0x00, 0xFC, 0x00, 0x00, 0xFC, 0x00,
  0x00, 0xFC, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xF8, 0x00, 0x01, 0xF8, 0x00,
  0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x1F, 0xC7, 0xF1, 0xF8, 0xFE, 0x3F,
  0x8F, 0xE0, 0x38, 0x1C, 0x07, 0x03, 0xC0, 0xE0, 0xF0, 0xFC, 0x3C, 0x0C,
  0x00, 0x7F, 0xFD, 0xFF, 0xF7, 0xFF, 0x9F, 0xFE, 0xFF, 0xFB, 0xFF, 0xE0,
  0x7F, 0x7F, 0x7F, 0x7E, 0xFE, 0xFE, 0xFE, 0x00, 0x00, 0x70, 0x00, 0x0E,
  0x00, 0x00, 0xE0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x38, 0x00, 0x03,
  0x80, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x01,
  0xC0, 0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x07, 0x00, 0x00,
  0x70, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00,
  0x38, 0x00, 0x03, 0x80, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00,
  0x0E, 0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00,
  0x07, 0x00, 0x00, 0x70, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x1F, 0xFC, 0x00, 0x3F, 0xFF, 0x80, 0x3F, 0xFF, 0xC0, 0x3F, 0xFF, 0xF0,
  0x1F, 0xC7, 0xF8, 0x1F, 0xC1, 0xFE, 0x1F, 0xC0, 0x7F, 0x0F, 0xC0, 0x3F,
  0x8F, 0xE0, 0x1F, 0xC7, 0xF0, 0x0F, 0xE3, 0xF0, 0x07, 0xF3, 0xF8, 0x03,
  0xF9, 0xFC, 0x01, 0xFC, 0xFC, 0x01, 0xFE, 0xFE, 0x00, 0xFE, 0x7F, 0x00,
  0x7F, 0x3F, 0x80, 0x3F, 0x9F, 0xC0, 0x1F, 0xCF, 0xE0, 0x1F, 0xEF, 0xE0,
  0x0F, 0xE7, 0xF0, 0x07, 0xF3, 0xF8, 0x03, 0xF9, 0xFC, 0x03, 0xF8, 0xFE,
  0x01, 0xFC, 0x7F, 0x00, 0xFE, 0x3F, 0x80, 0xFE, 0x1F, 0xE0, 0x7F, 0x0F,
  0xF8, 0xFF, 0x03, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0x80,
  0x1F, 0xFF, 0x00, 0x07, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x0F,
  0x80, 0x0F, 0x80, 0x07, 0xC0, 0x07, 0xE0, 0x0F, 0xF0, 0x3F, 0xF9, 0xFF,
  0xF8, 0xFF, 0xFC, 0xFF, 0xFE, 0x7F, 0xFF, 0x00, 0x3F, 0x80, 0x1F, 0x80,
  0x0F, 0xC0, 0x0F, 0xE0, 0x07, 0xF0, 0x03, 0xF8, 0x01, 0xF8, 0x01, 0xFC,
  0x00, 0xFE, 0x00, 0x7F, 0x00, 0x3F, 0x00, 0x1F, 0x80, 0x1F, 0xC0, 0x0F,
  0xE0, 0x07, 0xF0, 0x03, 0xF0, 0x01, 0xF8, 0x01, 0xFC, 0x00, 0xFE, 0x00,
  0x7F, 0x00, 0x3F, 0x00, 0x3F, 0x80, 0x1F, 0xC0, 0x00, 0x00, 0x01, 0xFE,
  0x00, 0x00, 0x7F, 0xFC, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xE0,
  0x0F, 0xFF, 0xFF, 0x00, 0xFF, 0x07, 0xFC, 0x07, 0xF0, 0x1F, 0xE0, 0x7F,
  0x00, 0x7F, 0x03, 0xF0, 0x03, 0xF8, 0x1F, 0x80, 0x1F, 0xC1, 0xF8, 0x00,
  0xFE, 0x0F, 0xC0, 0x0F, 0xE0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x07, 0xF0,
  0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x7F, 0x80, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x1F, 0xF8, 0x00, 0x01, 0xFF,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x7F, 0xC0, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x03, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0x81, 0xFF, 0xFF,
  0xFC, 0x1F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xF0,
  0x00, 0x00, 0x0F, 0xF8, 0x00, 0x0F, 0xFF, 0x80, 0x0F, 0xFF, 0xF0, 0x07,
  0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xC0, 0xFE, 0x1F, 0xF0, 0x7F, 0x01, 0xFC,
  0x1F, 0x80, 0x7F, 0x07, 0xE0, 0x1F, 0xC3, 0xF0, 0x07, 0xF0, 0xFC, 0x01,
  0xF8, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x7F, 0x80, 0x01,
  0xFF, 0xC0, 0x00, 0x7F, 0xE0, 0x00, 0x1F, 0xFC, 0x00, 0x07, 0xFF, 0x80,
  0x01, 0xFF, 0xE0, 0x00, 0x07, 0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x07, 0xF0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x7F, 0x3F, 0x80,
  0x3F, 0xCF, 0xE0, 0x0F, 0xE3, 0xF8, 0x07, 0xF8, 0xFF, 0x83, 0xFC, 0x3F,
  0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xE0,
  0x03, 0xFF, 0xE0, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0x00, 0x7F, 0x80, 0x00,
  0x7F, 0xC0, 0x00, 0x7F, 0xE0, 0x00, 0x7F, 0xE0, 0x00, 0x3F, 0xF0, 0x00,
  0x3F, 0xF8, 0x00, 0x3D, 0xFC, 0x00, 0x3C, 0xFE, 0x00, 0x3E, 0x7E, 0x00,
  0x3E, 0x7F, 0x00, 0x1E, 0x3F, 0x80, 0x1E, 0x1F, 0xC0, 0x1E, 0x0F, 0xC0,
  0x1F, 0x07, 0xE0, 0x1F, 0x07, 0xF0, 0x1F, 0x03, 0xF8, 0x1F, 0x01, 0xFC,
  0x0F, 0x80, 0xFC, 0x0F, 0x80, 0xFE, 0x0F, 0x80, 0x7F, 0x07, 0xFF, 0xFF,
  0xF7, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF,
  0xFE, 0x00, 0x03, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xFC, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0x80, 0x00,
  0x7F, 0xFF, 0xE0, 0x0F, 0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0x80, 0x7F, 0xFF,
  0xF0, 0x0F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0x80, 0x7C, 0x00, 0x00, 0x0F,
  0x80, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0x03, 0xE3, 0xF0, 0x00, 0x7F, 0xFF, 0x80, 0x1F, 0xFF, 0xF8, 0x03, 0xFF,
  0xFF, 0x80, 0x7F, 0xFF, 0xF0, 0x1F, 0xE1, 0xFF, 0x03, 0xF0, 0x1F, 0xE0,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xF0, 0xFE, 0x00, 0xFE, 0x1F,
  0xC0, 0x3F, 0x83, 0xF8, 0x07, 0xF0, 0x7F, 0x83, 0xFC, 0x0F, 0xFF, 0xFF,
  0x80, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xF8, 0x01, 0xFF, 0xFE, 0x00, 0x0F,
  0xFF, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x1F, 0xFE,
  0x00, 0x1F, 0xFF, 0x80, 0x1F, 0xFF, 0xE0, 0x1F, 0xFF, 0xF8, 0x1F, 0xC3,
  0xFC, 0x1F, 0x80, 0xFE, 0x0F, 0xC0, 0x3F, 0x0F, 0xC0, 0x00, 0x07, 0xE0,
  0x00, 0x07, 0xE0, 0x00, 0x03, 0xF0, 0x00, 0x03, 0xF8, 0xFC, 0x01, 0xF9,
  0xFF, 0x80, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xF8, 0x7F, 0xFF, 0xFC, 0x3F,
  0xE1, 0xFF, 0x1F, 0xE0, 0x7F, 0x8F, 0xE0, 0x1F, 0xCF, 0xE0, 0x0F, 0xE7,
  0xF0, 0x07, 0xF3, 0xF0, 0x03, 0xF9, 0xF8, 0x01, 0xF8, 0xFC, 0x01, 0xFC,
  0x7E, 0x00, 0xFE, 0x3F, 0x00, 0xFE, 0x1F, 0xC0, 0xFF, 0x0F, 0xF0, 0xFF,
  0x03, 0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x1F, 0xFF,
  0x80, 0x07, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xCF,
  0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xFF,
  0x9F, 0xFF, 0xFF, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xF8, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x1F, 0xC0, 0x00, 0x07, 0xE0, 0x00, 0x03, 0xF0, 0x00, 0x01, 0xF8, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x0F, 0xE0,
  0x00, 0x03, 0xF0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F,
  0x80, 0x00, 0x0F, 0xC0, 0x00, 0x07, 0xF0, 0x00, 0x01, 0xF8, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xF0, 0x00,
  0x01, 0xF8, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x07, 0xF0, 0x00,
  0x0F, 0xFF, 0x80, 0x07, 0xFF, 0xF0, 0x03, 0xFF, 0xFE, 0x01, 0xFF, 0xFF,
  0xC0, 0xFE, 0x0F, 0xF0, 0x3E, 0x01, 0xFC, 0x1F, 0x80, 0x3F, 0x07, 0xC0,
  0x0F, 0xC1, 0xF0, 0x03, 0xF0, 0x7C, 0x01, 0xF8, 0x1F, 0x00, 0xFC, 0x03,
  0xF0, 0x7F, 0x00, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0x80, 0x07, 0xFF, 0xE0,
  0x07, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0x81, 0xFE, 0x1F, 0xE0, 0xFE, 0x03,
  0xFC, 0x3F, 0x00, 0x7F, 0x1F, 0xC0, 0x1F, 0xC7, 0xE0, 0x07, 0xF3, 0xF8,
  0x01, 0xFC, 0xFE, 0x00, 0x7F, 0x3F, 0x80, 0x3F, 0x8F, 0xE0, 0x0F, 0xE3,
  0xFC, 0x07, 0xF0, 0xFF, 0x87, 0xFC, 0x3F, 0xFF, 0xFE, 0x07, 0xFF, 0xFF,
  0x00, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xC0, 0x03, 0xFF, 0xE0, 0x00, 0x3F,
  0xC0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0xFC, 0x00, 0x3F, 0xFF, 0x00,
  0x3F, 0xFF, 0xC0, 0x3F, 0xFF, 0xF0, 0x3F, 0xC3, 0xF8, 0x3F, 0xC0, 0xFE,
  0x1F, 0xC0, 0x3F, 0x1F, 0xC0, 0x1F, 0x8F, 0xE0, 0x0F, 0xC7, 0xE0, 0x07,
  0xE7, 0xF0, 0x03, 0xF3, 0xF8, 0x01, 0xF9, 0xFC, 0x01, 0xFC, 0xFE, 0x00,
  0xFE, 0x7F, 0x00, 0xFE, 0x3F, 0xC0, 0xFF, 0x1F, 0xF0, 0xFF, 0x87, 0xFF,
  0xFF, 0xC3, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xE0, 0x3F, 0xF3, 0xF0, 0x07,
  0xE3, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xFC, 0x00,
  0x00, 0x7E, 0x1F, 0xC0, 0x7E, 0x0F, 0xF0, 0xFF, 0x07, 0xFF, 0xFF, 0x01,
  0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0x00, 0x0F, 0xFF, 0x00,
  0x01, 0xFC, 0x00, 0x00, 0x07, 0xF0, 0x7F, 0x07, 0xF0, 0x7E, 0x0F, 0xE0,
  0xFE, 0x0F, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0x07, 0xF0, 0x7F, 0x07,
  0xE0, 0xFE, 0x0F, 0xE0, 0xFE, 0x00, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0,
  0x7E, 0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0x80, 0xFE, 0x03, 0xF8, 0x0F, 0xE0,
  0x03, 0x80, 0x1C, 0x00, 0x70, 0x03, 0xC0, 0x0E, 0x00, 0xF0, 0x0F, 0xC0,
  0x3C, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0xE0, 0x00,
  0x01, 0xF8, 0x00, 0x03, 0xFE, 0x00, 0x07, 0xFF, 0x80, 0x0F, 0xFF, 0xE0,
  0x1F, 0xFF, 0xF0, 0x1F, 0xFF, 0xE0, 0x3F, 0xFF, 0xC0, 0x1F, 0xFF, 0x80,
  0x0F, 0xFF, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x3F, 0xFE,
  0x00, 0x0F, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0x80, 0x07, 0xFF, 0xF8, 0x00,
  0x7F, 0xFF, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0x00, 0x01, 0xF8,
  0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x80, 0x1F, 0xFF, 0xFF, 0xC7, 0xFF,
  0xFF, 0xF1, 0xFF, 0xFF, 0xFC, 0x7F, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0x8F,
  0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF,
  0xFF, 0x1F, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0xF8, 0xFF,
  0xFF, 0xFE, 0x3F, 0xFF, 0xFF, 0x80, 0x04, 0x00, 0x00, 0x01, 0xE0, 0x00,
  0x00, 0x7E, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x0F, 0xFF, 0x00, 0x03, 0xFF,
  0xF8, 0x00, 0x7F, 0xFF, 0x80, 0x07, 0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xC0,
  0x01, 0xFF, 0xF0, 0x00, 0x1F, 0xFC, 0x00, 0x01, 0xFF, 0x00, 0x03, 0xFF,
  0xC0, 0x07, 0xFF, 0xE0, 0x0F, 0xFF, 0xF0, 0x1F, 0xFF, 0xE0, 0x3F, 0xFF,
  0xE0, 0x1F, 0xFF, 0xC0, 0x07, 0xFF, 0x80, 0x01, 0xFF, 0x00, 0x00, 0x7E,
  0x00, 0x00, 0x1C, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x80,
  0x01, 0xFF, 0xF0, 0x07, 0xFF, 0xF8, 0x0F, 0xFF, 0xFC, 0x1F, 0xFF, 0xFE,
  0x1F, 0xFF, 0xFE, 0x3F, 0xC1, 0xFF, 0x3F, 0x80, 0xFF, 0x7F, 0x00, 0x7F,
  0x7E, 0x00, 0x7F, 0xFE, 0x00, 0x7F, 0xFE, 0x00, 0x7F, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0xFE, 0x00, 0x01, 0xFC, 0x00, 0x07, 0xFC, 0x00, 0x0F, 0xF8,
  0x00, 0x1F, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x7F, 0x80, 0x00, 0xFE, 0x00,
  0x01, 0xF8, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xF0, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x0F, 0xE0, 0x00, 0x0F, 0xE0, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0xC0, 0x00,
  0x1F, 0xC0, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0x80, 0x00,
  0x01, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x3F, 0xE0, 0x1F, 0xF8, 0x00, 0x07,
  0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x7F, 0x00, 0x00, 0x3F, 0x80, 0x07, 0xE0,
  0x00, 0x00, 0xFE, 0x00, 0xFE, 0x00, 0x00, 0x03, 0xF0, 0x0F, 0xC0, 0x00,
  0x00, 0x0F, 0x80, 0xFC, 0x00, 0x00, 0x00, 0x3E, 0x07, 0xC0, 0x03, 0xF1,
  0xF1, 0xF0, 0x7C, 0x00, 0xFF, 0xCF, 0x07, 0x87, 0xE0, 0x1F, 0xFF, 0xF8,
  0x3C, 0x7E, 0x01, 0xF8, 0x7F, 0x81, 0xE3, 0xE0, 0x1F, 0x01, 0xF8, 0x0F,
  0x3E, 0x01, 0xF0, 0x0F, 0xC0, 0x79, 0xF0, 0x1F, 0x00, 0x7C, 0x03, 0xDF,
  0x00, 0xF0, 0x03, 0xE0, 0x1C, 0xF8, 0x0F, 0x80, 0x1E, 0x01, 0xE7, 0xC0,
  0x78, 0x00, 0xF0, 0x0F, 0x3C, 0x07, 0xC0, 0x0F, 0x00, 0xF3, 0xE0, 0x3C,
  0x00, 0x78, 0x07, 0x9F, 0x03, 0xE0, 0x07, 0x80, 0x78, 0xF8, 0x1F, 0x00,
  0x7C, 0x07, 0xC7, 0xC0, 0xF8, 0x07, 0xC0, 0x7C, 0x3E, 0x07, 0xC0, 0x7E,
  0x07, 0xC1, 0xF0, 0x3F, 0x07, 0xF8, 0xFC, 0x0F, 0x81, 0xFF, 0xFF, 0xFF,
  0xC0, 0x7E, 0x07, 0xFF, 0xBF, 0xFC, 0x01, 0xF0, 0x1F, 0xF8, 0xFF, 0x80,
  0x0F, 0xC0, 0x7E, 0x03, 0xF0, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x3F,
  0xE0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xE0, 0x03, 0x80, 0x00, 0x01, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF,
  0xFE, 0x00, 0x00, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F,
  0xF0, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x7F,
  0xF0, 0x00, 0x00, 0x7F, 0xF0, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00, 0xFF,
  0xF0, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x03, 0xFF, 0xF8, 0x00, 0x03, 0xFB,
  0xF8, 0x00, 0x07, 0xF3, 0xF8, 0x00, 0x07, 0xE3, 0xF8, 0x00, 0x0F, 0xE3,
  0xF8, 0x00, 0x0F, 0xC3, 0xF8, 0x00, 0x1F, 0xC3, 0xF8, 0x00, 0x1F, 0x83,
  0xF8, 0x00, 0x3F, 0x81, 0xFC, 0x00, 0x7F, 0x01, 0xFC, 0x00, 0x7F, 0x01,
  0xFC, 0x00, 0xFE, 0x01, 0xFC, 0x00, 0xFC, 0x01, 0xFC, 0x01, 0xFF, 0xFF,
  0xFC, 0x01, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xFE, 0x07, 0xFF, 0xFF,
  0xFE, 0x07, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xFE, 0x0F, 0xE0, 0x00,
  0xFE, 0x1F, 0xC0, 0x00, 0xFE, 0x1F, 0xC0, 0x00, 0xFE, 0x3F, 0x80, 0x00,
  0xFE, 0x3F, 0x80, 0x00, 0x7F, 0x7F, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00,
  0x7F, 0x01, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFF, 0xF8, 0x01, 0xFF, 0xFF,
  0xFC, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF,
  0xFF, 0x03, 0xF8, 0x00, 0xFF, 0x03, 0xF8, 0x00, 0x7F, 0x07, 0xF0, 0x00,
  0x7F, 0x07, 0xF0, 0x00, 0x7F, 0x07, 0xF0, 0x00, 0x7E, 0x07, 0xF0, 0x00,
  0xFE, 0x0F, 0xF0, 0x03, 0xFC, 0x0F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF,
  0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF,
  0xF8, 0x1F, 0xFF, 0xFF, 0xF8, 0x1F, 0xC0, 0x07, 0xFC, 0x1F, 0xC0, 0x01,
  0xFC, 0x1F, 0xC0, 0x01, 0xFC, 0x3F, 0x80, 0x01, 0xFC, 0x3F, 0x80, 0x01,
  0xFC, 0x3F, 0x80, 0x01, 0xFC, 0x3F, 0x80, 0x03, 0xF8, 0x7F, 0x00, 0x07,
  0xF8, 0x7F, 0x00, 0x0F, 0xF0, 0x7F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF,
  0xE0, 0x7F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFE,
  0x00, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x07, 0xFF,
  0xE0, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0xFF, 0xFF,
  0xFC, 0x01, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0x03, 0xFE, 0x07, 0xFC, 0x01,
  0xFF, 0x0F, 0xF0, 0x00, 0xFF, 0x0F, 0xE0, 0x00, 0x7F, 0x1F, 0xE0, 0x00,
  0x7F, 0x1F, 0xC0, 0x00, 0x7F, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x01, 0xF8, 0xFE, 0x00, 0x03,
  0xF8, 0xFF, 0x00, 0x07, 0xF8, 0xFF, 0x00, 0x07, 0xF0, 0x7F, 0x80, 0x1F,
  0xF0, 0x7F, 0xE0, 0x7F, 0xE0, 0x3F, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF,
  0x80, 0x1F, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xF8,
  0x00, 0x00, 0x7F, 0xC0, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF,
  0xE0, 0x03, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFC, 0x03, 0xFF, 0xFF,
  0xFC, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xF8, 0x03, 0xFE, 0x07, 0xF0, 0x01,
  0xFF, 0x07, 0xF0, 0x00, 0xFF, 0x07, 0xF0, 0x00, 0x7F, 0x07, 0xF0, 0x00,
  0x7F, 0x0F, 0xF0, 0x00, 0x7F, 0x0F, 0xE0, 0x00, 0x7F, 0x0F, 0xE0, 0x00,
  0x7F, 0x0F, 0xE0, 0x00, 0x7F, 0x0F, 0xE0, 0x00, 0x7F, 0x1F, 0xC0, 0x00,
  0x7F, 0x1F, 0xC0, 0x00, 0xFE, 0x1F, 0xC0, 0x00, 0xFE, 0x1F, 0xC0, 0x00,
  0xFE, 0x1F, 0xC0, 0x01, 0xFE, 0x3F, 0x80, 0x01, 0xFC, 0x3F, 0x80, 0x01,
  0xFC, 0x3F, 0x80, 0x03, 0xF8, 0x3F, 0x80, 0x07, 0xF8, 0x7F, 0x00, 0x0F,
  0xF0, 0x7F, 0x00, 0x1F, 0xF0, 0x7F, 0x00, 0x7F, 0xE0, 0x7F, 0xFF, 0xFF,
  0xC0, 0x7F, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFE,
  0x00, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0xFF,
  0xFF, 0x01, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF,
  0xFE, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xF8, 0x00,
  0x00, 0x07, 0xF0, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x07, 0xF0, 0x00,
  0x00, 0x07, 0xF0, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x00, 0x0F, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF,
  0xE0, 0x1F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF,
  0xE0, 0x1F, 0xC0, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF,
  0xC0, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF,
  0x80, 0x00, 0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFF, 0x01, 0xFF, 0xFF,
  0xFE, 0x01, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFF,
  0xFE, 0x03, 0xF8, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x03, 0xF8, 0x00,
  0x00, 0x03, 0xF8, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x07, 0xF0, 0x00,
  0x00, 0x07, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xE0, 0x07, 0xFF, 0xFF,
  0xE0, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF,
  0xC0, 0x0F, 0xFF, 0xFF, 0xC0, 0x0F, 0xE0, 0x00, 0x00, 0x1F, 0xC0, 0x00,
  0x00, 0x1F, 0xC0, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x1F, 0xC0, 0x00,
  0x00, 0x1F, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x03, 0xFF,
  0xF8, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xFF, 0x80, 0x0F, 0xFF,
  0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFC, 0x07, 0xFC, 0x0F, 0xF8,
  0x00, 0xFF, 0x0F, 0xF0, 0x00, 0x3F, 0x87, 0xF0, 0x00, 0x1F, 0xC7, 0xF0,
  0x00, 0x0F, 0xE3, 0xF8, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x01, 0xFC,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x7F,
  0x00, 0x3F, 0xFF, 0x3F, 0x00, 0x1F, 0xFF, 0xBF, 0x80, 0x0F, 0xFF, 0x9F,
  0xC0, 0x07, 0xFF, 0xCF, 0xE0, 0x03, 0xFF, 0xE7, 0xF0, 0x03, 0xFF, 0xF3,
  0xF8, 0x00, 0x01, 0xF9, 0xFC, 0x00, 0x01, 0xF8, 0xFF, 0x00, 0x00, 0xFC,
  0x7F, 0x80, 0x00, 0xFE, 0x3F, 0xC0, 0x00, 0xFF, 0x0F, 0xF0, 0x00, 0xFF,
  0x87, 0xFC, 0x00, 0xFF, 0x81, 0xFF, 0x81, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF,
  0xE0, 0x3F, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFE, 0xF8, 0x03, 0xFF, 0xFC,
  0x78, 0x00, 0x7F, 0xFC, 0x3C, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x01, 0xFC,
  0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x01, 0xFC, 0x07, 0xF0, 0x00, 0x3F, 0x80,
  0xFE, 0x00, 0x0F, 0xE0, 0x1F, 0xC0, 0x01, 0xFC, 0x07, 0xF0, 0x00, 0x3F,
  0x80, 0xFE, 0x00, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0xFE, 0x03, 0xF8, 0x00,
  0x3F, 0x80, 0xFF, 0x00, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0xFE, 0x03, 0xF8,
  0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x07, 0xF0, 0x0F, 0xFF, 0xFF, 0xFE, 0x03,
  0xFF, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xFF,
  0x01, 0xFF, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xF8, 0x0F, 0xE0, 0x00,
  0x7F, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x01, 0xFC, 0x07, 0xF0,
  0x00, 0x7F, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x01, 0xFC, 0x07,
  0xF0, 0x00, 0x3F, 0x80, 0xFE, 0x00, 0x0F, 0xE0, 0x1F, 0xC0, 0x01, 0xFC,
  0x07, 0xF0, 0x00, 0x3F, 0x80, 0xFE, 0x00, 0x07, 0xF0, 0x1F, 0xC0, 0x00,
  0xFE, 0x03, 0xF8, 0x00, 0x3F, 0x80, 0x7F, 0x00, 0x07, 0xF0, 0x1F, 0xC0,
  0x00, 0xFE, 0x00, 0x01, 0xFC, 0x07, 0xF0, 0x3F, 0x80, 0xFE, 0x03, 0xF8,
  0x0F, 0xE0, 0x3F, 0x81, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0x7F, 0x01, 0xFC,
  0x0F, 0xE0, 0x3F, 0x80, 0xFE, 0x03, 0xF8, 0x0F, 0xE0, 0x7F, 0x01, 0xFC,
  0x07, 0xF0, 0x1F, 0xC0, 0x7F, 0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0x80, 0xFE,
  0x03, 0xF8, 0x1F, 0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0xFE,
  0x03, 0xF8, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x00,
  0x3F, 0x80, 0x00, 0x0F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x80,
  0x00, 0x07, 0xF0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07,
  0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xF0, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x7F,
  0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x0F, 0xE0, 0xFE, 0x03, 0xFC, 0x1F, 0xC0, 0x7F, 0x03, 0xF8, 0x0F, 0xE0,
  0xFE, 0x01, 0xFC, 0x1F, 0xC0, 0x3F, 0x83, 0xF8, 0x0F, 0xE0, 0x7F, 0x01,
  0xFC, 0x0F, 0xF0, 0xFF, 0x81, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF, 0xF8, 0x03,
  0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0x80, 0x03, 0xFF, 0xE0, 0x00, 0x1F, 0xE0,
  0x00, 0x00, 0x00, 0xFE, 0x00, 0x0F, 0xF0, 0x0F, 0xF0, 0x00, 0xFF, 0x00,
  0x7F, 0x00, 0x1F, 0xF0, 0x03, 0xF8, 0x01, 0xFF, 0x00, 0x1F, 0xC0, 0x1F,
  0xE0, 0x00, 0xFE, 0x01, 0xFE, 0x00, 0x0F, 0xE0, 0x1F, 0xE0, 0x00, 0x7F,
  0x01, 0xFE, 0x00, 0x03, 0xF8, 0x1F, 0xE0, 0x00, 0x1F, 0xC1, 0xFE, 0x00,
  0x00, 0xFE, 0x1F, 0xE0, 0x00, 0x0F, 0xE3, 0xFE, 0x00, 0x00, 0x7F, 0x3F,
  0xC0, 0x00, 0x03, 0xFB, 0xFC, 0x00, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x01,
  0xFF, 0xFE, 0x00, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x00, 0x7F, 0xFF, 0xC0,
  0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x01, 0xFF,
  0x9F, 0xE0, 0x00, 0x0F, 0xF8, 0xFF, 0x00, 0x00, 0x7F, 0x83, 0xFC, 0x00,
  0x03, 0xF8, 0x1F, 0xF0, 0x00, 0x1F, 0xC0, 0x7F, 0x80, 0x01, 0xFC, 0x01,
  0xFE, 0x00, 0x0F, 0xE0, 0x0F, 0xF0, 0x00, 0x7F, 0x00, 0x3F, 0xC0, 0x03,
  0xF8, 0x01, 0xFF, 0x00, 0x3F, 0x80, 0x07, 0xF8, 0x01, 0xFC, 0x00, 0x3F,
  0xE0, 0x0F, 0xE0, 0x00, 0xFF, 0x00, 0x7F, 0x00, 0x07, 0xFC, 0x03, 0xF8,
  0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x01, 0xFC, 0x00, 0x01, 0xFC,
  0x00, 0x03, 0xF8, 0x00, 0x03, 0xF8, 0x00, 0x03, 0xF8, 0x00, 0x03, 0xF8,
  0x00, 0x07, 0xF0, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xF0,
  0x00, 0x07, 0xF0, 0x00, 0x0F, 0xE0, 0x00, 0x0F, 0xE0, 0x00, 0x0F, 0xE0,
  0x00, 0x0F, 0xE0, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0xC0, 0x00, 0x1F, 0xC0,
  0x00, 0x1F, 0xC0, 0x00, 0x1F, 0xC0, 0x00, 0x3F, 0x80, 0x00, 0x3F, 0x80,
  0x00, 0x3F, 0x80, 0x00, 0x3F, 0x80, 0x00, 0x3F, 0x80, 0x00, 0x7F, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0x7F, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x01, 0xFF,
  0x80, 0x03, 0xFF, 0x80, 0xFF, 0xC0, 0x01, 0xFF, 0x80, 0x7F, 0xE0, 0x01,
  0xFF, 0xC0, 0x3F, 0xF0, 0x00, 0xFF, 0xE0, 0x3F, 0xF8, 0x00, 0xFF, 0xF0,
  0x1F, 0xFC, 0x00, 0x7F, 0xF8, 0x0F, 0xFE, 0x00, 0x7D, 0xF8, 0x07, 0xEF,
  0x00, 0x3E, 0xFC, 0x03, 0xF7, 0x80, 0x3F, 0xFE, 0x03, 0xFB, 0xC0, 0x1F,
  0x7F, 0x01, 0xFD, 0xE0, 0x1F, 0xBF, 0x00, 0xFE, 0xF0, 0x0F, 0x9F, 0x80,
  0x7E, 0x78, 0x0F, 0xDF, 0xC0, 0x7F, 0x3E, 0x07, 0xCF, 0xE0, 0x3F, 0x9F,
  0x07, 0xE7, 0xF0, 0x1F, 0xCF, 0x83, 0xE3, 0xF0, 0x0F, 0xE7, 0xC3, 0xF1,
  0xF8, 0x07, 0xE3, 0xE1, 0xF9, 0xFC, 0x07, 0xF1, 0xF0, 0xF8, 0xFE, 0x03,
  0xF8, 0xF8, 0xFC, 0x7F, 0x01, 0xFC, 0x7C, 0x7C, 0x3F, 0x00, 0xFC, 0x3E,
  0x7E, 0x1F, 0x80, 0x7E, 0x1F, 0x3E, 0x1F, 0xC0, 0x7F, 0x0F, 0xBF, 0x0F,
  0xE0, 0x3F, 0x87, 0xDF, 0x07, 0xE0, 0x1F, 0xC3, 0xFF, 0x83, 0xF0, 0x0F,
  0xC1, 0xFF, 0xC3, 0xF8, 0x0F, 0xE0, 0xFF, 0xC1, 0xFC, 0x07, 0xF0, 0x7F,
  0xE0, 0xFE, 0x03, 0xF8, 0x3F, 0xE0, 0x7E, 0x01, 0xFC, 0x1F, 0xF0, 0x3F,
  0x00, 0xFC, 0x0F, 0xF0, 0x3F, 0x80, 0xFE, 0x07, 0xF8, 0x1F, 0xC0, 0x7F,
  0x03, 0xF8, 0x0F, 0xC0, 0x00, 0x01, 0xFE, 0x00, 0x07, 0xE0, 0x3F, 0xC0,
  0x01, 0xFC, 0x07, 0xFC, 0x00, 0x3F, 0x80, 0xFF, 0x80, 0x07, 0xF0, 0x1F,
  0xF0, 0x00, 0xFC, 0x07, 0xFF, 0x00, 0x3F, 0x80, 0xFF, 0xE0, 0x07, 0xF0,
  0x1F, 0xFC, 0x00, 0xFE, 0x03, 0xFF, 0xC0, 0x1F, 0x80, 0xFF, 0xF8, 0x03,
  0xF0, 0x1F, 0xFF, 0x80, 0xFE, 0x03, 0xFB, 0xF0, 0x1F, 0xC0, 0x7E, 0x7E,
  0x03, 0xF8, 0x0F, 0xC7, 0xE0, 0x7E, 0x03, 0xF8, 0xFC, 0x0F, 0xC0, 0x7F,
  0x1F, 0x83, 0xF8, 0x0F, 0xE1, 0xF8, 0x7F, 0x01, 0xF8, 0x3F, 0x0F, 0xE0,
  0x3F, 0x07, 0xF1, 0xF8, 0x0F, 0xE0, 0x7E, 0x3F, 0x01, 0xFC, 0x0F, 0xCF,
  0xE0, 0x3F, 0x00, 0xFD, 0xFC, 0x07, 0xE0, 0x1F, 0xBF, 0x81, 0xFC, 0x03,
  0xF7, 0xE0, 0x3F, 0x80, 0x3F, 0xFC, 0x07, 0xF0, 0x07, 0xFF, 0x80, 0xFC,
  0x00, 0xFF, 0xF0, 0x1F, 0x80, 0x0F, 0xFC, 0x07, 0xF0, 0x01, 0xFF, 0x80,
  0xFE, 0x00, 0x3F, 0xF0, 0x1F, 0xC0, 0x03, 0xFE, 0x03, 0xF0, 0x00, 0x7F,
  0xC0, 0x7E, 0x00, 0x07, 0xF0, 0x1F, 0xC0, 0x00, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x01,
  0xFF, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0xC0,
  0x3F, 0xF0, 0x3F, 0xF8, 0x1F, 0xF0, 0x03, 0xFE, 0x07, 0xF0, 0x00, 0x7F,
  0x83, 0xF8, 0x00, 0x0F, 0xF1, 0xFE, 0x00, 0x03, 0xFC, 0x7F, 0x00, 0x00,
  0x7F, 0x3F, 0x80, 0x00, 0x1F, 0xCF, 0xE0, 0x00, 0x07, 0xF7, 0xF0, 0x00,
  0x01, 0xFD, 0xFC, 0x00, 0x00, 0x7F, 0x7F, 0x00, 0x00, 0x1F, 0xDF, 0xC0,
  0x00, 0x07, 0xFF, 0xE0, 0x00, 0x03, 0xFB, 0xF8, 0x00, 0x00, 0xFE, 0xFE,
  0x00, 0x00, 0x3F, 0xBF, 0x80, 0x00, 0x0F, 0xEF, 0xE0, 0x00, 0x07, 0xF3,
  0xF8, 0x00, 0x01, 0xFC, 0xFE, 0x00, 0x00, 0xFE, 0x3F, 0xC0, 0x00, 0x7F,
  0x8F, 0xF0, 0x00, 0x1F, 0xC1, 0xFE, 0x00, 0x0F, 0xE0, 0x7F, 0xC0, 0x0F,
  0xF8, 0x1F, 0xFC, 0x0F, 0xFC, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF,
  0xFF, 0x00, 0x0F, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x1F,
  0xFF, 0x80, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x03,
  0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xE0, 0x3F,
  0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xC1, 0xFE, 0x00, 0xFF, 0x83, 0xF8,
  0x00, 0xFF, 0x07, 0xF0, 0x00, 0xFE, 0x0F, 0xE0, 0x01, 0xFC, 0x1F, 0xC0,
  0x03, 0xF8, 0x7F, 0x00, 0x07, 0xF0, 0xFE, 0x00, 0x1F, 0xC1, 0xFC, 0x00,
  0x3F, 0x83, 0xF8, 0x00, 0xFE, 0x07, 0xF0, 0x07, 0xFC, 0x1F, 0xFF, 0xFF,
  0xF0, 0x3F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFE,
  0x03, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0x80, 0x0F, 0xE0, 0x00, 0x00,
  0x1F, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x0F,
  0xE0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x01, 0xFF, 0xF8, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x01, 0xFF, 0xFF, 0xF0,
  0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0xC0, 0x3F, 0xF0, 0x3F,
  0xF0, 0x1F, 0xF0, 0x03, 0xFE, 0x07, 0xF8, 0x00, 0x7F, 0x83, 0xFC, 0x00,
  0x0F, 0xF1, 0xFE, 0x00, 0x03, 0xFC, 0x7F, 0x00, 0x00, 0x7F, 0x3F, 0x80,
  0x00, 0x1F, 0xCF, 0xE0, 0x00, 0x07, 0xF3, 0xF0, 0x00, 0x01, 0xFD, 0xFC,
  0x00, 0x00, 0x7F, 0x7F, 0x00, 0x00, 0x1F, 0xDF, 0x80, 0x00, 0x07, 0xFF,
  0xE0, 0x00, 0x03, 0xFB, 0xF8, 0x00, 0x00, 0xFE, 0xFE, 0x00, 0x00, 0x3F,
  0xBF, 0x80, 0x00, 0x0F, 0xEF, 0xE0, 0x01, 0x87, 0xF3, 0xF8, 0x00, 0xF1,
  0xFC, 0xFE, 0x00, 0x7C, 0xFE, 0x3F, 0xC0, 0x3F, 0xFF, 0x8F, 0xF0, 0x07,
  0xFF, 0xC1, 0xFE, 0x01, 0xFF, 0xE0, 0x7F, 0xC0, 0x3F, 0xF8, 0x1F, 0xFC,
  0x0F, 0xFC, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFF, 0xC0, 0x0F,
  0xFF, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0x9F, 0x80,
  0x01, 0xFF, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x60, 0x00, 0x01, 0xFF, 0xFF,
  0xF0, 0x00, 0xFF, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFF, 0x80, 0x7F, 0xFF,
  0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xFC, 0x0F, 0xE0,
  0x03, 0xFE, 0x0F, 0xF0, 0x00, 0xFF, 0x07, 0xF0, 0x00, 0x3F, 0x83, 0xF8,
  0x00, 0x1F, 0xC1, 0xFC, 0x00, 0x0F, 0xC0, 0xFE, 0x00, 0x07, 0xE0, 0xFE,
  0x00, 0x07, 0xF0, 0x7F, 0x00, 0x07, 0xF0, 0x3F, 0x80, 0x0F, 0xF0, 0x1F,
  0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x07,
  0xFF, 0xFF, 0xF0, 0x03, 0xFF, 0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0xFF, 0x01,
  0xFC, 0x00, 0x7F, 0x80, 0xFE, 0x00, 0x1F, 0xC0, 0x7F, 0x00, 0x0F, 0xE0,
  0x3F, 0x80, 0x07, 0xF0, 0x1F, 0xC0, 0x03, 0xF8, 0x1F, 0xC0, 0x01, 0xFC,
  0x0F, 0xE0, 0x01, 0xFC, 0x07, 0xF0, 0x00, 0xFE, 0x03, 0xF8, 0x00, 0x7F,
  0x01, 0xFC, 0x00, 0x3F, 0x81, 0xFC, 0x00, 0x1F, 0xC0, 0xFE, 0x00, 0x0F,
  0xE0, 0x7F, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x7F,
  0xFF, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFC, 0x01, 0xFF, 0xFF,
  0xF8, 0x0F, 0xFF, 0xFF, 0xF0, 0x3F, 0xC0, 0x7F, 0xC1, 0xFE, 0x00, 0xFF,
  0x07, 0xF0, 0x01, 0xFC, 0x3F, 0x80, 0x07, 0xF0, 0xFE, 0x00, 0x1F, 0xC3,
  0xF8, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x00, 0xFF,
  0xE0, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x0F, 0xFF,
  0xFE, 0x00, 0x1F, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0xF8, 0x00, 0x03, 0xFF,
  0xF0, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01, 0xFC,
  0x00, 0x00, 0x07, 0xF3, 0xF8, 0x00, 0x1F, 0xCF, 0xE0, 0x00, 0x7E, 0x3F,
  0x80, 0x03, 0xF8, 0xFF, 0x00, 0x1F, 0xE3, 0xFF, 0x01, 0xFF, 0x07, 0xFF,
  0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF,
  0xF0, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x3F, 0xF0, 0x00, 0x7F, 0xFF, 0xFF,
  0xF7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFE, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x07, 0xF0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x01, 0xFC, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x07,
  0xF0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x7F, 0x00,
  0x00, 0x0F, 0xE0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x7F,
  0x07, 0xF0, 0x00, 0x7F, 0x07, 0xF0, 0x00, 0xFE, 0x0F, 0xE0, 0x00, 0xFE,
  0x0F, 0xE0, 0x00, 0xFE, 0x0F, 0xE0, 0x00, 0xFE, 0x0F, 0xE0, 0x00, 0xFE,
  0x0F, 0xE0, 0x01, 0xFC, 0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xC0, 0x01, 0xFC,
  0x1F, 0xC0, 0x01, 0xFC, 0x1F, 0xC0, 0x01, 0xFC, 0x3F, 0x80, 0x03, 0xF8,
  0x3F, 0x80, 0x03, 0xF8, 0x3F, 0x80, 0x03, 0xF8, 0x3F, 0x80, 0x03, 0xF8,
  0x3F, 0x80, 0x07, 0xF0, 0x7F, 0x00, 0x07, 0xF0, 0x7F, 0x00, 0x07, 0xF0,
  0x7F, 0x00, 0x07, 0xF0, 0x7F, 0x00, 0x07, 0xF0, 0x7F, 0x00, 0x0F, 0xE0,
  0xFE, 0x00, 0x0F, 0xE0, 0xFE, 0x00, 0x0F, 0xE0, 0xFE, 0x00, 0x0F, 0xE0,
  0xFE, 0x00, 0x1F, 0xC0, 0xFE, 0x00, 0x1F, 0xC0, 0xFF, 0x00, 0x3F, 0x80,
  0xFF, 0xC0, 0xFF, 0x80, 0x7F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFE, 0x00,
  0x3F, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x0F, 0xFF, 0xE0, 0x00,
  0x01, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x03, 0xF9, 0xFC, 0x00, 0x0F, 0xE7,
  0xF0, 0x00, 0x7F, 0x1F, 0xC0, 0x01, 0xFC, 0x7F, 0x00, 0x0F, 0xE1, 0xFC,
  0x00, 0x3F, 0x87, 0xF0, 0x01, 0xFC, 0x1F, 0xC0, 0x07, 0xF0, 0x3F, 0x00,
  0x3F, 0x80, 0xFC, 0x00, 0xFC, 0x03, 0xF0, 0x07, 0xF0, 0x0F, 0xC0, 0x1F,
  0x80, 0x3F, 0x80, 0xFE, 0x00, 0xFE, 0x03, 0xF0, 0x03, 0xF8, 0x1F, 0xC0,
  0x0F, 0xE0, 0x7E, 0x00, 0x1F, 0x83, 0xF8, 0x00, 0x7E, 0x0F, 0xC0, 0x01,
  0xF8, 0x7E, 0x00, 0x07, 0xE1, 0xF8, 0x00, 0x1F, 0x8F, 0xC0, 0x00, 0x7E,
  0x3F, 0x00, 0x01, 0xF9, 0xF8, 0x00, 0x07, 0xE7, 0xE0, 0x00, 0x0F, 0xFF,
  0x00, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x01, 0xFC, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x00,
  0xFE, 0x00, 0x7F, 0x80, 0x1F, 0xFF, 0xC0, 0x0F, 0xF0, 0x03, 0xFB, 0xF8,
  0x01, 0xFE, 0x00, 0x7F, 0x7F, 0x00, 0x7F, 0xC0, 0x1F, 0xCF, 0xE0, 0x0F,
  0xF8, 0x03, 0xF9, 0xFC, 0x03, 0xFF, 0x00, 0xFE, 0x3F, 0x80, 0x7F, 0xE0,
  0x1F, 0xC7, 0xF0, 0x1F, 0xFC, 0x07, 0xF0, 0x7E, 0x03, 0xFF, 0x80, 0xFE,
  0x0F, 0xC0, 0x7D, 0xF0, 0x1F, 0x81, 0xF8, 0x1F, 0xBE, 0x07, 0xF0, 0x3F,
  0x03, 0xE7, 0xC0, 0xFC, 0x07, 0xE0, 0xFC, 0xF8, 0x3F, 0x80, 0xFC, 0x1F,
  0x1F, 0x07, 0xE0, 0x1F, 0x83, 0xE3, 0xE0, 0xFC, 0x03, 0xF0, 0xFC, 0x7C,
  0x3F, 0x00, 0x7E, 0x1F, 0x0F, 0x87, 0xE0, 0x0F, 0xC7, 0xE1, 0xF1, 0xF8,
  0x01, 0xF8, 0xF8, 0x3E, 0x3F, 0x00, 0x3F, 0x3F, 0x07, 0xCF, 0xC0, 0x07,
  0xE7, 0xC0, 0xF9, 0xF8, 0x00, 0xFC, 0xF8, 0x1F, 0x3E, 0x00, 0x1F, 0xBE,
  0x03, 0xEF, 0xC0, 0x01, 0xF7, 0xC0, 0x7D, 0xF0, 0x00, 0x3F, 0xF8, 0x0F,
  0xFE, 0x00, 0x07, 0xFE, 0x01, 0xFF, 0x80, 0x00, 0xFF, 0xC0, 0x3F, 0xF0,
  0x00, 0x1F, 0xF0, 0x07, 0xFC, 0x00, 0x03, 0xFE, 0x00, 0xFF, 0x80, 0x00,
  0x7F, 0x80, 0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x03, 0xFC, 0x00, 0x01, 0xFC,
  0x00, 0x7F, 0x80, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x00, 0x07, 0xF0, 0x01,
  0xFC, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x0F, 0xF0, 0x07, 0xFC, 0x00, 0xFF,
  0x00, 0x1F, 0xE0, 0x07, 0xF8, 0x00, 0xFF, 0x00, 0x7F, 0x80, 0x03, 0xFC,
  0x07, 0xF8, 0x00, 0x1F, 0xE0, 0x7F, 0x80, 0x00, 0xFF, 0x07, 0xF8, 0x00,
  0x03, 0xFC, 0x3F, 0x80, 0x00, 0x1F, 0xE3, 0xF8, 0x00, 0x00, 0x7F, 0x3F,
  0xC0, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x00, 0x1F, 0xFF, 0xC0, 0x00, 0x00,
  0x7F, 0xFC, 0x00, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x00, 0x0F, 0xFC, 0x00,
  0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x00, 0x1F,
  0xF0, 0x00, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00,
  0x00, 0xFF, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x00, 0xFF, 0xFE,
  0x00, 0x00, 0x0F, 0xE7, 0xF0, 0x00, 0x00, 0xFF, 0x3F, 0xC0, 0x00, 0x0F,
  0xF1, 0xFE, 0x00, 0x00, 0xFF, 0x07, 0xF8, 0x00, 0x07, 0xF0, 0x3F, 0xC0,
  0x00, 0x7F, 0x01, 0xFE, 0x00, 0x07, 0xF8, 0x07, 0xF8, 0x00, 0x7F, 0x80,
  0x3F, 0xC0, 0x07, 0xF8, 0x01, 0xFF, 0x00, 0x7F, 0x80, 0x07, 0xF8, 0x07,
  0xFC, 0x00, 0x3F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF7, 0xF8, 0x00, 0x7F,
  0xBF, 0xC0, 0x07, 0xF8, 0xFE, 0x00, 0x3F, 0x87, 0xF8, 0x03, 0xFC, 0x3F,
  0xC0, 0x3F, 0xC0, 0xFE, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0x3F, 0xC1,
  0xFE, 0x00, 0xFE, 0x0F, 0xE0, 0x07, 0xF0, 0xFE, 0x00, 0x3F, 0x8F, 0xE0,
  0x00, 0xFE, 0x7F, 0x00, 0x07, 0xF7, 0xF0, 0x00, 0x3F, 0xFF, 0x00, 0x01,
  0xFF, 0xF8, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x3F, 0xF8, 0x00, 0x01, 0xFF,
  0x80, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x0F, 0xE0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00,
  0x3F, 0x80, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x7F,
  0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x0F, 0xE0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF,
  0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x07, 0xFC, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x07, 0xF8, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFF,
  0x01, 0xFF, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFF,
  0xC0, 0x3F, 0xFF, 0xFF, 0xE0, 0x00, 0x00, 0x7F, 0xF8, 0x03, 0xFF, 0x80,
  0x1F, 0xFC, 0x00, 0xFF, 0xE0, 0x0F, 0xFF, 0x00, 0x7E, 0x00, 0x03, 0xF0,
  0x00, 0x1F, 0x80, 0x01, 0xFC, 0x00, 0x0F, 0xC0, 0x00, 0x7E, 0x00, 0x03,
  0xF0, 0x00, 0x1F, 0x80, 0x01, 0xFC, 0x00, 0x0F, 0xC0, 0x00, 0x7E, 0x00,
  0x03, 0xF0, 0x00, 0x1F, 0x80, 0x01, 0xFC, 0x00, 0x0F, 0xC0, 0x00, 0x7E,
  0x00, 0x03, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x00,
  0x7E, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xF8, 0x00, 0x0F, 0xC0,
  0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x01, 0xF8, 0x00, 0x0F,
  0xC0, 0x00, 0x7E, 0x00, 0x07, 0xF0, 0x00, 0x3F, 0x00, 0x01, 0xFF, 0xC0,
  0x0F, 0xFE, 0x00, 0x7F, 0xF0, 0x07, 0xFF, 0x80, 0x3F, 0xFC, 0x00, 0x81,
  0xC3, 0xC7, 0x8F, 0x0E, 0x1C, 0x38, 0x70, 0xE1, 0xC3, 0xC7, 0x8F, 0x1E,
  0x1C, 0x38, 0x70, 0xE1, 0xC3, 0x87, 0x8F, 0x1E, 0x3C, 0x38, 0x70, 0xE1,
  0xC3, 0x87, 0x0F, 0x1E, 0x3C, 0x78, 0xF0, 0x00, 0x7F, 0xF8, 0x03, 0xFF,
  0xC0, 0x1F, 0xFC, 0x00, 0xFF, 0xE0, 0x07, 0xFF, 0x00, 0x01, 0xF8, 0x00,
  0x1F, 0xC0, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00, 0x01, 0xF8,
  0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00, 0x03,
  0xF8, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00,
  0x03, 0xF8, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x7F,
  0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00,
  0x7F, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0,
  0x00, 0x7F, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x01, 0xFF,
  0xE0, 0x0F, 0xFE, 0x00, 0x7F, 0xF0, 0x03, 0xFF, 0x80, 0x3F, 0xFC, 0x00,
  0x00, 0x1F, 0x80, 0x00, 0xFE, 0x00, 0x0F, 0xF0, 0x00, 0x7F, 0x80, 0x07,
  0xFC, 0x00, 0x7F, 0xE0, 0x03, 0xFF, 0x80, 0x3E, 0xFC, 0x01, 0xF3, 0xE0,
  0x1F, 0x1F, 0x01, 0xF8, 0xF8, 0x0F, 0x87, 0xE0, 0xFC, 0x3F, 0x07, 0xC0,
  0xF8, 0x7C, 0x07, 0xC7, 0xE0, 0x3E, 0x3E, 0x01, 0xFB, 0xF0, 0x0F, 0xDF,
  0x00, 0x3F, 0xF0, 0x01, 0xF0, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFF, 0xE0, 0xF8, 0xF0, 0xF1, 0xE1,
  0xC3, 0xC3, 0x80, 0x00, 0x1F, 0xF0, 0x00, 0x7F, 0xFF, 0x00, 0xFF, 0xFF,
  0xC0, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xF8, 0x7F, 0x03, 0xFC, 0x3F, 0x00,
  0xFE, 0x1F, 0x80, 0x7E, 0x00, 0x00, 0x7F, 0x00, 0x00, 0xFF, 0x80, 0x1F,
  0xFF, 0xC0, 0x7F, 0xFF, 0xC0, 0xFF, 0xFF, 0xE0, 0xFF, 0xF7, 0xF0, 0xFF,
  0x83, 0xF8, 0xFF, 0x01, 0xF8, 0x7F, 0x00, 0xFC, 0x7F, 0x00, 0xFE, 0x3F,
  0x80, 0x7F, 0x1F, 0xC0, 0x7F, 0x8F, 0xF0, 0xFF, 0x87, 0xFF, 0xFF, 0xC3,
  0xFF, 0xFF, 0xE0, 0xFF, 0xF7, 0xF8, 0x3F, 0xF3, 0xFC, 0x07, 0xE0, 0x00,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xE0, 0x00, 0x00, 0xFC, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00, 0xFE, 0x3F, 0x80, 0x1F,
  0x9F, 0xFC, 0x03, 0xF7, 0xFF, 0xC0, 0xFF, 0xFF, 0xF8, 0x1F, 0xFF, 0xFF,
  0x83, 0xFF, 0x0F, 0xF0, 0x7F, 0x80, 0xFF, 0x0F, 0xE0, 0x1F, 0xE3, 0xF8,
  0x01, 0xFC, 0x7F, 0x00, 0x3F, 0x8F, 0xC0, 0x07, 0xF1, 0xF8, 0x00, 0xFE,
  0x7F, 0x00, 0x1F, 0xCF, 0xC0, 0x03, 0xF9, 0xF8, 0x00, 0xFE, 0x3F, 0x00,
  0x1F, 0xC7, 0xE0, 0x03, 0xF9, 0xFC, 0x00, 0xFE, 0x3F, 0xC0, 0x3F, 0xC7,
  0xF8, 0x0F, 0xF0, 0xFF, 0x83, 0xFC, 0x1F, 0xFF, 0xFF, 0x07, 0xFF, 0xFF,
  0xC0, 0xFF, 0xFF, 0xF0, 0x1F, 0x9F, 0xFC, 0x00, 0x00, 0xFC, 0x00, 0x00,
  0x00, 0x1F, 0xE0, 0x00, 0x3F, 0xFC, 0x00, 0x7F, 0xFF, 0x80, 0x7F, 0xFF,
  0xE0, 0x7F, 0xFF, 0xF0, 0x7F, 0x83, 0xFC, 0x7F, 0x00, 0xFE, 0x3F, 0x00,
  0x7F, 0x3F, 0x80, 0x3F, 0x9F, 0x80, 0x00, 0x1F, 0xC0, 0x00, 0x0F, 0xE0,
  0x00, 0x07, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xF8, 0x00, 0x01, 0xFC,
  0x00, 0x00, 0xFE, 0x00, 0x00, 0x7F, 0x00, 0x7F, 0x3F, 0x80, 0x3F, 0x9F,
  0xE0, 0x3F, 0x87, 0xF8, 0x3F, 0x83, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xC0,
  0x3F, 0xFF, 0xC0, 0x0F, 0xFF, 0x80, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x00,
  0x03, 0xF8, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x07,
  0xE0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x00, 0xFC, 0x00, 0x00, 0x0F, 0xE0, 0x01, 0xFC, 0x7F, 0x00, 0x3F,
  0xF3, 0xF8, 0x03, 0xFF, 0xDF, 0x80, 0x7F, 0xFF, 0xFC, 0x07, 0xFF, 0xFF,
  0xE0, 0x3F, 0xC3, 0xFF, 0x03, 0xFC, 0x0F, 0xF8, 0x3F, 0xC0, 0x3F, 0x81,
  0xFC, 0x01, 0xFC, 0x1F, 0xC0, 0x07, 0xE0, 0xFE, 0x00, 0x3F, 0x07, 0xF0,
  0x03, 0xF8, 0x7F, 0x00, 0x1F, 0x83, 0xF8, 0x00, 0xFC, 0x1F, 0xC0, 0x07,
  0xE0, 0xFE, 0x00, 0x3F, 0x07, 0xF0, 0x03, 0xF0, 0x3F, 0x80, 0x3F, 0x81,
  0xFC, 0x01, 0xFC, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC3, 0xFF, 0x01, 0xFF,
  0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xFC, 0x00, 0xFF, 0xCF,
  0xE0, 0x01, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x7F, 0xFC, 0x00,
  0x7F, 0xFF, 0x00, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xF0, 0x7F, 0x87, 0xF8,
  0x7F, 0x01, 0xFE, 0x7F, 0x00, 0x7F, 0x3F, 0x80, 0x3F, 0xBF, 0x80, 0x1F,
  0xDF, 0xC0, 0x0F, 0xEF, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFD, 0xFC, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7F, 0x00,
  0x00, 0x3F, 0x80, 0x3F, 0x9F, 0xE0, 0x3F, 0x87, 0xF8, 0x3F, 0xC3, 0xFF,
  0xFF, 0xC0, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0x80, 0x0F, 0xFF, 0x80, 0x00,
  0xFE, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x1F, 0xF0, 0x0F, 0xF8, 0x07, 0xFE,
  0x01, 0xFF, 0x80, 0xFE, 0x00, 0x3F, 0x80, 0x0F, 0xC0, 0x03, 0xF0, 0x01,
  0xFC, 0x03, 0xFF, 0xF1, 0xFF, 0xF8, 0x7F, 0xFE, 0x1F, 0xFF, 0x80, 0xFE,
  0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF0, 0x00, 0xFC, 0x00, 0x7F, 0x00,
  0x1F, 0xC0, 0x07, 0xE0, 0x01, 0xF8, 0x00, 0xFE, 0x00, 0x3F, 0x80, 0x0F,
  0xE0, 0x03, 0xF0, 0x00, 0xFC, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x07, 0xF0,
  0x01, 0xF8, 0x00, 0x7E, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x07, 0xC3, 0xF8,
  0x01, 0xFF, 0x9F, 0x80, 0x1F, 0xFE, 0xFC, 0x01, 0xFF, 0xFF, 0xE0, 0x1F,
  0xFF, 0xFF, 0x01, 0xFE, 0x1F, 0xF8, 0x1F, 0xE0, 0x3F, 0x80, 0xFE, 0x01,
  0xFC, 0x0F, 0xE0, 0x0F, 0xE0, 0x7F, 0x00, 0x3F, 0x07, 0xF0, 0x01, 0xF8,
  0x3F, 0x80, 0x0F, 0x81, 0xF8, 0x00, 0x7C, 0x1F, 0xC0, 0x07, 0xE0, 0xFE,
  0x00, 0x3F, 0x07, 0xF0, 0x01, 0xF0, 0x3F, 0x80, 0x1F, 0x81, 0xFC, 0x00,
  0xFC, 0x0F, 0xE0, 0x0F, 0xE0, 0x7F, 0x80, 0xFF, 0x03, 0xFE, 0x1F, 0xF0,
  0x0F, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0xFC, 0x01, 0xFF, 0xF7, 0xE0, 0x07,
  0xFE, 0x7F, 0x00, 0x0F, 0xC3, 0xF0, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x01,
  0xFC, 0x0F, 0xE0, 0x0F, 0xC0, 0x7F, 0x00, 0xFE, 0x03, 0xFC, 0x1F, 0xE0,
  0x1F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xE0, 0x01, 0xFF, 0xFC, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xE0,
  0x00, 0x00, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x03, 0xF0, 0x00, 0x00, 0xFE, 0x0F,
  0xC0, 0x1F, 0xCF, 0xFE, 0x03, 0xFB, 0xFF, 0xE0, 0x7F, 0xFF, 0xFE, 0x0F,
  0xFF, 0xFF, 0xC3, 0xFF, 0x07, 0xF8, 0x7F, 0x80, 0x7F, 0x0F, 0xE0, 0x0F,
  0xE1, 0xFC, 0x01, 0xFC, 0x7F, 0x00, 0x3F, 0x0F, 0xE0, 0x07, 0xE1, 0xFC,
  0x01, 0xFC, 0x3F, 0x00, 0x3F, 0x87, 0xE0, 0x07, 0xF1, 0xFC, 0x00, 0xFC,
  0x3F, 0x80, 0x1F, 0x87, 0xF0, 0x07, 0xF0, 0xFC, 0x00, 0xFE, 0x1F, 0x80,
  0x1F, 0xC7, 0xF0, 0x03, 0xF0, 0xFE, 0x00, 0x7E, 0x1F, 0xC0, 0x1F, 0xC3,
  0xF0, 0x03, 0xF8, 0xFE, 0x00, 0x7F, 0x1F, 0xC0, 0x0F, 0xC0, 0x01, 0xFC,
  0x07, 0xF0, 0x1F, 0x80, 0x7E, 0x03, 0xF8, 0x0F, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xE0, 0x3F, 0x80, 0xFE,
  0x03, 0xF8, 0x0F, 0xC0, 0x3F, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0x7E,
  0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0x80, 0xFC, 0x03, 0xF0, 0x1F, 0xC0, 0x7F,
  0x01, 0xFC, 0x07, 0xE0, 0x1F, 0x80, 0xFE, 0x03, 0xF8, 0x00, 0x00, 0x0F,
  0xE0, 0x01, 0xFC, 0x00, 0x3F, 0x80, 0x07, 0xE0, 0x00, 0xFC, 0x00, 0x3F,
  0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xF0, 0x00, 0xFE,
  0x00, 0x1F, 0xC0, 0x03, 0xF8, 0x00, 0x7E, 0x00, 0x1F, 0xC0, 0x03, 0xF8,
  0x00, 0x7F, 0x00, 0x0F, 0xC0, 0x01, 0xF8, 0x00, 0x7F, 0x00, 0x0F, 0xE0,
  0x01, 0xFC, 0x00, 0x3F, 0x00, 0x07, 0xE0, 0x01, 0xFC, 0x00, 0x3F, 0x80,
  0x07, 0xF0, 0x00, 0xFC, 0x00, 0x1F, 0x80, 0x07, 0xF0, 0x00, 0xFE, 0x00,
  0x1F, 0x80, 0x03, 0xF0, 0x00, 0xFE, 0x00, 0x1F, 0xC0, 0x03, 0xF8, 0x00,
  0x7E, 0x00, 0x0F, 0xC0, 0x03, 0xF8, 0x03, 0xFF, 0x00, 0x7F, 0xC0, 0x0F,
  0xF8, 0x03, 0xFE, 0x00, 0x7E, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x1F,
  0x80, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8, 0x00,
  0x00, 0x3F, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x07,
  0xF0, 0x00, 0x00, 0x7F, 0x00, 0xFE, 0x07, 0xE0, 0x3F, 0xC0, 0x7E, 0x07,
  0xF8, 0x0F, 0xE0, 0xFF, 0x00, 0xFE, 0x1F, 0xC0, 0x0F, 0xE3, 0xF8, 0x00,
  0xFC, 0x7F, 0x00, 0x0F, 0xCF, 0xE0, 0x01, 0xFD, 0xFC, 0x00, 0x1F, 0xFF,
  0x80, 0x01, 0xFF, 0xF8, 0x00, 0x1F, 0xFF, 0x80, 0x03, 0xFF, 0xFC, 0x00,
  0x3F, 0xFF, 0xC0, 0x03, 0xFE, 0xFE, 0x00, 0x3F, 0xCF, 0xE0, 0x03, 0xF0,
  0xFE, 0x00, 0x7F, 0x07, 0xF0, 0x07, 0xF0, 0x7F, 0x00, 0x7F, 0x07, 0xF8,
  0x07, 0xE0, 0x3F, 0x80, 0x7E, 0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0xC0, 0xFE,
  0x01, 0xFC, 0x0F, 0xC0, 0x1F, 0xE0, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0x80,
  0x7E, 0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0x80, 0xFC, 0x03, 0xF0, 0x1F, 0xC0,
  0x7F, 0x01, 0xFC, 0x07, 0xE0, 0x3F, 0x80, 0xFE, 0x03, 0xF8, 0x0F, 0xC0,
  0x3F, 0x01, 0xFC, 0x07, 0xF0, 0x1F, 0xC0, 0x7E, 0x03, 0xF8, 0x0F, 0xE0,
  0x3F, 0x80, 0xFC, 0x03, 0xF0, 0x1F, 0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xE0,
  0x1F, 0x80, 0xFE, 0x03, 0xF8, 0x00, 0x07, 0xF0, 0xFC, 0x03, 0xF0, 0x07,
  0xE3, 0xFF, 0x0F, 0xFC, 0x07, 0xEF, 0xFF, 0x3F, 0xFE, 0x0F, 0xFF, 0xFF,
  0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0xF8, 0x7F, 0xF0, 0xFF,
  0x0F, 0xE0, 0x3F, 0xC0, 0x7F, 0x0F, 0xE0, 0x3F, 0x80, 0x7F, 0x1F, 0xC0,
  0x3F, 0x80, 0x7E, 0x1F, 0xC0, 0x3F, 0x00, 0x7E, 0x1F, 0xC0, 0x3F, 0x00,
  0xFE, 0x1F, 0x80, 0x7F, 0x00, 0xFE, 0x3F, 0x80, 0x7F, 0x00, 0xFC, 0x3F,
  0x80, 0x7F, 0x00, 0xFC, 0x3F, 0x80, 0x7E, 0x01, 0xFC, 0x3F, 0x00, 0x7E,
  0x01, 0xFC, 0x3F, 0x00, 0xFE, 0x01, 0xFC, 0x7F, 0x00, 0xFE, 0x01, 0xF8,
  0x7F, 0x00, 0xFE, 0x01, 0xF8, 0x7F, 0x00, 0xFC, 0x03, 0xF8, 0x7E, 0x01,
  0xFC, 0x03, 0xF8, 0x7E, 0x01, 0xFC, 0x03, 0xF8, 0xFE, 0x01, 0xFC, 0x03,
  0xF0, 0xFE, 0x01, 0xF8, 0x03, 0xF0, 0xFE, 0x01, 0xF8, 0x07, 0xF0, 0x07,
  0xF0, 0xFE, 0x00, 0xFE, 0x7F, 0xF0, 0x1F, 0x9F, 0xFF, 0x03, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0xFE, 0x1F, 0xF8, 0x3F, 0xC3, 0xFC, 0x03, 0xF8, 0x7F,
  0x00, 0x7F, 0x0F, 0xE0, 0x0F, 0xE3, 0xF8, 0x01, 0xF8, 0x7F, 0x00, 0x3F,
  0x0F, 0xC0, 0x0F, 0xE1, 0xF8, 0x01, 0xFC, 0x7F, 0x00, 0x3F, 0x8F, 0xE0,
  0x07, 0xE1, 0xFC, 0x00, 0xFC, 0x3F, 0x00, 0x3F, 0x87, 0xE0, 0x07, 0xF1,
  0xFC, 0x00, 0xFE, 0x3F, 0x80, 0x1F, 0x87, 0xF0, 0x03, 0xF0, 0xFC, 0x00,
  0xFE, 0x3F, 0x80, 0x1F, 0xC7, 0xF0, 0x03, 0xF8, 0xFE, 0x00, 0x7E, 0x00,
  0x00, 0x1F, 0xE0, 0x00, 0x1F, 0xFF, 0x00, 0x1F, 0xFF, 0xE0, 0x0F, 0xFF,
  0xFC, 0x07, 0xFF, 0xFF, 0x83, 0xFC, 0x1F, 0xE1, 0xFE, 0x03, 0xFC, 0xFF,
  0x00, 0xFF, 0x3F, 0x80, 0x1F, 0xDF, 0xC0, 0x07, 0xF7, 0xF0, 0x01, 0xFD,
  0xFC, 0x00, 0x7F, 0xFE, 0x00, 0x1F, 0xFF, 0x80, 0x07, 0xFF, 0xE0, 0x03,
  0xFB, 0xF8, 0x00, 0xFE, 0xFE, 0x00, 0x3F, 0xBF, 0x80, 0x1F, 0xCF, 0xF0,
  0x0F, 0xF3, 0xFC, 0x07, 0xF8, 0x7F, 0x83, 0xFC, 0x1F, 0xFF, 0xFE, 0x03,
  0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0x80, 0x0F, 0xFF, 0x80, 0x00, 0x7F, 0x00,
  0x00, 0x01, 0xFC, 0x3F, 0x00, 0x0F, 0xCF, 0xFE, 0x00, 0x7E, 0xFF, 0xF8,
  0x07, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x01, 0xFF, 0x87, 0xF8, 0x0F,
  0xF0, 0x1F, 0xE0, 0xFF, 0x00, 0xFF, 0x07, 0xF0, 0x03, 0xF8, 0x3F, 0x80,
  0x1F, 0xC1, 0xF8, 0x00, 0xFE, 0x0F, 0xC0, 0x07, 0xF0, 0xFE, 0x00, 0x3F,
  0x87, 0xF0, 0x01, 0xFC, 0x3F, 0x00, 0x1F, 0xC1, 0xF8, 0x00, 0xFE, 0x1F,
  0xC0, 0x07, 0xF0, 0xFE, 0x00, 0x7F, 0x07, 0xF8, 0x07, 0xF8, 0x3F, 0xC0,
  0x7F, 0x81, 0xFF, 0x87, 0xF8, 0x1F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFC,
  0x07, 0xF7, 0xFF, 0xC0, 0x3F, 0x1F, 0xF8, 0x01, 0xF8, 0x7F, 0x00, 0x1F,
  0xC0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x3F, 0x00,
  0x00, 0x03, 0xF8, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0xFE, 0x00, 0x00,
  0x07, 0xE0, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x87, 0xF0,
  0x0F, 0xFE, 0x7F, 0x01, 0xFF, 0xF7, 0xE0, 0x3F, 0xFF, 0x7E, 0x07, 0xFF,
  0xFF, 0xE0, 0xFF, 0x07, 0xFE, 0x1F, 0xE0, 0x3F, 0xE3, 0xFC, 0x03, 0xFC,
  0x3F, 0x80, 0x1F, 0xC7, 0xF0, 0x01, 0xFC, 0x7F, 0x00, 0x1F, 0xC7, 0xF0,
  0x01, 0xF8, 0xFE, 0x00, 0x1F, 0x8F, 0xE0, 0x03, 0xF8, 0xFE, 0x00, 0x3F,
  0x8F, 0xE0, 0x03, 0xF8, 0xFE, 0x00, 0x7F, 0x0F, 0xE0, 0x07, 0xF0, 0xFE,
  0x00, 0xFF, 0x0F, 0xF0, 0x1F, 0xF0, 0x7F, 0x87, 0xFF, 0x07, 0xFF, 0xFF,
  0xE0, 0x3F, 0xFF, 0x7E, 0x03, 0xFF, 0xEF, 0xE0, 0x1F, 0xFC, 0xFE, 0x00,
  0x7F, 0x0F, 0xC0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x1F, 0x80,
  0x00, 0x03, 0xF8, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8, 0x00, 0x07,
  0xF0, 0xF0, 0x7F, 0x3F, 0x07, 0xE7, 0xE0, 0x7E, 0xFE, 0x0F, 0xFF, 0xE0,
  0xFF, 0xFE, 0x0F, 0xFC, 0x00, 0xFF, 0x00, 0x0F, 0xE0, 0x01, 0xFC, 0x00,
  0x1F, 0xC0, 0x01, 0xF8, 0x00, 0x1F, 0x80, 0x03, 0xF8, 0x00, 0x3F, 0x80,
  0x03, 0xF8, 0x00, 0x3F, 0x00, 0x03, 0xF0, 0x00, 0x7F, 0x00, 0x07, 0xF0,
  0x00, 0x7F, 0x00, 0x07, 0xE0, 0x00, 0xFE, 0x00, 0x0F, 0xE0, 0x00, 0xFE,
  0x00, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0xFF, 0xF8, 0x03, 0xFF, 0xFC, 0x07,
  0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0x0F, 0xE0, 0xFF, 0x1F, 0xC0, 0x7F, 0x1F,
  0xC0, 0x7F, 0x1F, 0xE0, 0x00, 0x1F, 0xFC, 0x00, 0x1F, 0xFF, 0xC0, 0x0F,
  0xFF, 0xF0, 0x07, 0xFF, 0xF8, 0x03, 0xFF, 0xFC, 0x00, 0x7F, 0xFE, 0x00,
  0x0F, 0xFE, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFE, 0xFC, 0x00, 0xFE, 0xFE,
  0x00, 0xFE, 0xFF, 0x03, 0xFC, 0x7F, 0xFF, 0xF8, 0x7F, 0xFF, 0xF8, 0x3F,
  0xFF, 0xE0, 0x1F, 0xFF, 0xC0, 0x03, 0xFE, 0x00, 0x03, 0xF0, 0x1F, 0xC0,
  0x7F, 0x01, 0xFC, 0x07, 0xE0, 0x3F, 0x80, 0xFE, 0x1F, 0xFF, 0x7F, 0xFD,
  0xFF, 0xFF, 0xFF, 0xC7, 0xF0, 0x1F, 0xC0, 0x7E, 0x01, 0xF8, 0x0F, 0xE0,
  0x3F, 0x80, 0xFE, 0x03, 0xF0, 0x0F, 0xC0, 0x7F, 0x01, 0xFC, 0x07, 0xE0,
  0x1F, 0x80, 0xFE, 0x03, 0xF8, 0x0F, 0xE0, 0x3F, 0xF0, 0xFF, 0xC3, 0xFF,
  0x07, 0xFC, 0x0F, 0xE0, 0x0F, 0xC0, 0x0F, 0xE1, 0xF8, 0x01, 0xFC, 0x7F,
  0x00, 0x3F, 0x0F, 0xE0, 0x0F, 0xE1, 0xFC, 0x01, 0xFC, 0x3F, 0x00, 0x3F,
  0x87, 0xE0, 0x07, 0xE1, 0xFC, 0x00, 0xFC, 0x3F, 0x80, 0x3F, 0x87, 0xF0,
  0x07, 0xF0, 0xFC, 0x00, 0xFE, 0x1F, 0x80, 0x1F, 0x87, 0xF0, 0x03, 0xF0,
  0xFE, 0x00, 0xFE, 0x1F, 0x80, 0x1F, 0xC3, 0xF0, 0x03, 0xF0, 0xFE, 0x00,
  0x7E, 0x1F, 0xC0, 0x1F, 0xC3, 0xF8, 0x07, 0xF8, 0x7F, 0x01, 0xFF, 0x0F,
  0xF0, 0x7F, 0xC1, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF, 0xFF, 0x03, 0xFF, 0xEF,
  0xE0, 0x3F, 0xF9, 0xFC, 0x01, 0xF8, 0x00, 0x00, 0xFE, 0x00, 0x7F, 0x7F,
  0x00, 0x3F, 0xBF, 0x80, 0x3F, 0x8F, 0xC0, 0x1F, 0xC7, 0xE0, 0x1F, 0xC3,
  0xF0, 0x0F, 0xC1, 0xFC, 0x0F, 0xE0, 0xFE, 0x07, 0xE0, 0x7F, 0x07, 0xF0,
  0x3F, 0x83, 0xF0, 0x0F, 0xC3, 0xF8, 0x07, 0xE1, 0xF8, 0x03, 0xF1, 0xFC,
  0x01, 0xF8, 0xFC, 0x00, 0xFC, 0xFC, 0x00, 0x7E, 0x7E, 0x00, 0x3F, 0x7E,
  0x00, 0x0F, 0xBF, 0x00, 0x07, 0xFF, 0x00, 0x03, 0xFF, 0x80, 0x01, 0xFF,
  0x80, 0x00, 0xFF, 0x80, 0x00, 0x7F, 0xC0, 0x00, 0x3F, 0xC0, 0x00, 0x1F,
  0xE0, 0x00, 0x00, 0xFE, 0x03, 0xF8, 0x0F, 0xFF, 0xC0, 0x7F, 0x01, 0xFF,
  0xF8, 0x1F, 0xE0, 0x3F, 0x7F, 0x03, 0xFC, 0x0F, 0xEF, 0xE0, 0xFF, 0x81,
  0xF9, 0xFC, 0x1F, 0xF0, 0x7F, 0x3F, 0x83, 0xFE, 0x0F, 0xC3, 0xF0, 0xFF,
  0xC3, 0xF8, 0x7E, 0x1E, 0xF8, 0x7E, 0x0F, 0xC7, 0xDF, 0x1F, 0xC1, 0xF8,
  0xFB, 0xE3, 0xF0, 0x3F, 0x1E, 0x7C, 0x7E, 0x07, 0xE7, 0xCF, 0x9F, 0x80,
  0xFC, 0xF1, 0xF3, 0xF0, 0x1F, 0xBE, 0x3E, 0xFC, 0x03, 0xF7, 0x87, 0xDF,
  0x80, 0x7E, 0xF0, 0xFF, 0xE0, 0x0F, 0xFE, 0x1F, 0xFC, 0x01, 0xFF, 0x83,
  0xFF, 0x00, 0x3F, 0xF0, 0x7F, 0xE0, 0x07, 0xFC, 0x0F, 0xF8, 0x00, 0x7F,
  0x81, 0xFF, 0x00, 0x0F, 0xF0, 0x3F, 0xC0, 0x01, 0xFC, 0x07, 0xF8, 0x00,
  0x3F, 0x80, 0xFE, 0x00, 0x00, 0x03, 0xFC, 0x07, 0xF8, 0x1F, 0xE0, 0x7F,
  0x80, 0x7F, 0x03, 0xF8, 0x03, 0xF8, 0x3F, 0x80, 0x1F, 0xE3, 0xF8, 0x00,
  0x7F, 0x3F, 0x80, 0x03, 0xF9, 0xFC, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x7F,
  0xFC, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x01, 0xFF, 0x80, 0x00,
  0x1F, 0xFE, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x1F, 0xDF, 0xC0, 0x01, 0xFC,
  0xFE, 0x00, 0x1F, 0xE7, 0xF8, 0x00, 0xFE, 0x1F, 0xC0, 0x0F, 0xE0, 0xFE,
  0x00, 0xFF, 0x07, 0xF8, 0x0F, 0xF0, 0x1F, 0xC0, 0xFF, 0x00, 0xFF, 0x00,
  0x0F, 0xE0, 0x03, 0xF0, 0x7F, 0x00, 0x3F, 0x83, 0xF8, 0x01, 0xF8, 0x1F,
  0xC0, 0x1F, 0xC0, 0xFE, 0x00, 0xFC, 0x03, 0xF8, 0x0F, 0xE0, 0x1F, 0xC0,
  0x7E, 0x00, 0xFE, 0x07, 0xE0, 0x07, 0xF0, 0x3F, 0x00, 0x3F, 0x83, 0xF0,
  0x01, 0xFC, 0x1F, 0x80, 0x0F, 0xE1, 0xF8, 0x00, 0x3F, 0x0F, 0xC0, 0x01,
  0xF8, 0xFC, 0x00, 0x0F, 0xC7, 0xC0, 0x00, 0x7F, 0x7E, 0x00, 0x03, 0xFB,
  0xE0, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0xFF, 0xF0, 0x00, 0x03, 0xFF, 0x80,
  0x00, 0x1F, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00,
  0x3F, 0xC0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x7F,
  0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x03, 0xF8, 0x00,
  0x01, 0xFF, 0x80, 0x00, 0x1F, 0xFC, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x07,
  0xF8, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xC0, 0xFF,
  0xFF, 0xF0, 0x3F, 0xFF, 0xF8, 0x1F, 0xFF, 0xFE, 0x07, 0xFF, 0xFF, 0x80,
  0x00, 0x3F, 0xC0, 0x00, 0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x00, 0x07, 0xF8,
  0x00, 0x03, 0xFC, 0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x3F, 0xC0, 0x00, 0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x00, 0x07,
  0xF8, 0x00, 0x03, 0xFC, 0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0x00, 0x00,
  0x7F, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF,
  0xE0, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x0F, 0xC0, 0x0F, 0xF0, 0x07, 0xFC,
  0x01, 0xFE, 0x00, 0xFF, 0x80, 0x3E, 0x00, 0x0F, 0x80, 0x07, 0xE0, 0x01,
  0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x03, 0xE0, 0x00, 0xF8,
  0x00, 0x3E, 0x00, 0x0F, 0x80, 0x07, 0xE0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
  0x3F, 0x00, 0x7F, 0x80, 0x1F, 0x80, 0x07, 0xE0, 0x03, 0xFC, 0x00, 0x3F,
  0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0,
  0x01, 0xF0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x80, 0x03, 0xE0, 0x01,
  0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xF8, 0x01, 0xFE, 0x00, 0x7F,
  0x80, 0x0F, 0xE0, 0x01, 0xF8, 0x00, 0x00, 0x78, 0x03, 0xC0, 0x1C, 0x01,
  0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x1C, 0x01, 0xE0, 0x0F, 0x00, 0x78,
  0x03, 0xC0, 0x1C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x3C, 0x01,
  0xE0, 0x0F, 0x00, 0x78, 0x03, 0x80, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78,
  0x03, 0x80, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0x80, 0x3C, 0x01,
  0xE0, 0x0F, 0x00, 0x70, 0x07, 0x80, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x70,
  0x07, 0x80, 0x3C, 0x00, 0x00, 0x7E, 0x00, 0x1F, 0xC0, 0x07, 0xF0, 0x01,
  0xFE, 0x00, 0x7F, 0x80, 0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x1F,
  0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x0F, 0x80,
  0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x80, 0x03, 0xF0, 0x00,
  0xFF, 0x00, 0x1F, 0x80, 0x07, 0xE0, 0x07, 0xF8, 0x03, 0xF0, 0x00, 0xF8,
  0x00, 0x3E, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
  0x1F, 0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x1F,
  0x80, 0x07, 0xC0, 0x01, 0xF0, 0x07, 0xFC, 0x01, 0xFE, 0x00, 0xFF, 0x80,
  0x3F, 0xC0, 0x0F, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0xFF, 0x80, 0x07, 0xFF,
  0x03, 0xDF, 0xFE, 0x0F, 0xF0, 0x7F, 0xFB, 0x80, 0xFF, 0xE0, 0x01, 0xFF,
  0x00, 0x03, 0xF0 };

const GFXglyph FreeSansBoldOblique24pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  13,    0,    1 },   // 0x20 ' '
  {     0,  14,  34,  16,    5,  -33 },   // 0x21 '!'
  {    60,  18,  12,  22,    8,  -33 },   // 0x22 '"'
  {    87,  29,  33,  26,    2,  -31 },   // 0x23 '#'
  {   207,  26,  42,  26,    3,  -35 },   // 0x24 '$'
  {   344,  36,  34,  42,    6,  -32 },   // 0x25 '%'
  {   497,  29,  35,  34,    4,  -33 },   // 0x26 '&'
  {   624,   7,  12,  11,    8,  -33 },   // 0x27 '''
  {   635,  17,  44,  16,    4,  -33 },   // 0x28 '('
  {   729,  17,  44,  16,    0,  -34 },   // 0x29 ')'
  {   823,  15,  15,  18,    7,  -33 },   // 0x2A '*'
  {   852,  24,  22,  27,    4,  -21 },   // 0x2B '+'
  {   918,  10,  15,  13,    1,   -6 },   // 0x2C ','
  {   937,  14,   6,  16,    3,  -15 },   // 0x2D '-'
  {   948,   8,   7,  13,    3,   -6 },   // 0x2E '.'
  {   955,  20,  34,  13,    0,  -32 },   // 0x2F '/'
  {  1040,  25,  35,  26,    4,  -33 },   // 0x30 '0'
  {  1150,  17,  33,  26,    8,  -32 },   // 0x31 '1'
  {  1221,  29,  34,  26,    1,  -33 },   // 0x32 '2'
  {  1345,  26,  35,  26,    3,  -33 },   // 0x33 '3'
  {  1459,  25,  32,  26,    3,  -31 },   // 0x34 '4'
  {  1559,  27,  34,  26,    3,  -32 },   // 0x35 '5'
  {  1674,  25,  35,  26,    4,  -33 },   // 0x36 '6'
  {  1784,  26,  33,  26,    6,  -32 },   // 0x37 '7'
  {  1892,  26,  35,  26,    3,  -33 },   // 0x38 '8'
  {  2006,  25,  35,  26,    4,  -33 },   // 0x39 '9'
  {  2116,  12,  25,  16,    5,  -24 },   // 0x3A ':'
  {  2154,  14,  33,  16,    3,  -24 },   // 0x3B ';'
  {  2212,  26,  23,  27,    4,  -22 },   // 0x3C '<'
  {  2287,  26,  18,  27,    3,  -19 },   // 0x3D '='
  {  2346,  26,  23,  27,    1,  -21 },   // 0x3E '>'
  {  2421,  24,  35,  29,    8,  -34 },   // 0x3F '?'
  {  2526,  45,  41,  46,    3,  -34 },   // 0x40 '@'
  {  2757,  32,  34,  34,    1,  -33 },   // 0x41 'A'
  {  2893,  32,  34,  34,    4,  -33 },   // 0x42 'B'
  {  3029,  32,  36,  34,    5,  -34 },   // 0x43 'C'
  {  3173,  32,  34,  34,    4,  -33 },   // 0x44 'D'
  {  3309,  32,  34,  31,    4,  -33 },   // 0x45 'E'
  {  3445,  32,  34,  29,    3,  -33 },   // 0x46 'F'
  {  3581,  33,  36,  37,    5,  -34 },   // 0x47 'G'
  {  3730,  35,  34,  34,    3,  -33 },   // 0x48 'H'
  {  3879,  14,  34,  13,    3,  -33 },   // 0x49 'I'
  {  3939,  27,  35,  26,    3,  -33 },   // 0x4A 'J'
  {  4058,  37,  34,  34,    3,  -33 },   // 0x4B 'K'
  {  4216,  24,  34,  29,    4,  -33 },   // 0x4C 'L'
  {  4318,  41,  34,  39,    3,  -33 },   // 0x4D 'M'
  {  4493,  35,  34,  34,    3,  -33 },   // 0x4E 'N'
  {  4642,  34,  36,  37,    5,  -34 },   // 0x4F 'O'
  {  4795,  31,  34,  31,    4,  -33 },   // 0x50 'P'
  {  4927,  34,  37,  37,    5,  -34 },   // 0x51 'Q'
  {  5085,  33,  34,  34,    4,  -33 },   // 0x52 'R'
  {  5226,  30,  36,  31,    4,  -34 },   // 0x53 'S'
  {  5361,  28,  34,  29,    7,  -33 },   // 0x54 'T'
  {  5480,  32,  35,  34,    6,  -33 },   // 0x55 'U'
  {  5620,  30,  34,  31,    8,  -33 },   // 0x56 'V'
  {  5748,  43,  34,  44,    8,  -33 },   // 0x57 'W'
  {  5931,  37,  34,  31,    1,  -33 },   // 0x58 'X'
  {  6089,  29,  34,  31,    9,  -33 },   // 0x59 'Y'
  {  6213,  33,  34,  29,    1,  -33 },   // 0x5A 'Z'
  {  6354,  21,  43,  16,    1,  -33 },   // 0x5B '['
  {  6467,   7,  36,  13,    6,  -34 },   // 0x5C '\'
  {  6499,  21,  43,  16,   -1,  -33 },   // 0x5D ']'
  {  6612,  21,  20,  27,    6,  -32 },   // 0x5E '^'
  {  6665,  29,   4,  26,   -3,    6 },   // 0x5F '_'
  {  6680,   7,   7,  16,    8,  -35 },   // 0x60 '`'
  {  6687,  25,  26,  26,    2,  -24 },   // 0x61 'a'
  {  6769,  27,  35,  29,    3,  -33 },   // 0x62 'b'
  {  6888,  25,  26,  26,    4,  -24 },   // 0x63 'c'
  {  6970,  29,  35,  29,    4,  -33 },   // 0x64 'd'
  {  7097,  25,  26,  26,    3,  -24 },   // 0x65 'e'
  {  7179,  18,  34,  16,    4,  -33 },   // 0x66 'f'
  {  7256,  29,  35,  29,    2,  -24 },   // 0x67 'g'
  {  7383,  27,  34,  29,    3,  -33 },   // 0x68 'h'
  {  7498,  14,  34,  13,    3,  -33 },   // 0x69 'i'
  {  7558,  19,  44,  13,   -2,  -33 },   // 0x6A 'j'
  {  7663,  28,  34,  26,    3,  -33 },   // 0x6B 'k'
  {  7782,  14,  34,  13,    3,  -33 },   // 0x6C 'l'
  {  7842,  40,  25,  42,    3,  -24 },   // 0x6D 'm'
  {  7967,  27,  25,  29,    3,  -24 },   // 0x6E 'n'
  {  8052,  26,  26,  29,    4,  -24 },   // 0x6F 'o'
  {  8137,  29,  35,  29,    1,  -24 },   // 0x70 'p'
  {  8264,  28,  35,  29,    3,  -24 },   // 0x71 'q'
  {  8387,  20,  25,  18,    3,  -24 },   // 0x72 'r'
  {  8450,  24,  26,  26,    3,  -24 },   // 0x73 's'
  {  8528,  14,  32,  16,    5,  -30 },   // 0x74 't'
  {  8584,  27,  26,  29,    4,  -24 },   // 0x75 'u'
  {  8672,  25,  25,  26,    6,  -24 },   // 0x76 'v'
  {  8751,  35,  25,  37,    6,  -24 },   // 0x77 'w'
  {  8861,  29,  25,  26,    1,  -24 },   // 0x78 'x'
  {  8952,  29,  35,  26,    2,  -24 },   // 0x79 'y'
  {  9079,  26,  25,  23,    1,  -24 },   // 0x7A 'z'
  {  9161,  18,  43,  18,    4,  -33 },   // 0x7B '{'
  {  9258,  13,  43,  13,    3,  -33 },   // 0x7C '|'
  {  9328,  18,  43,  18,    2,  -33 },   // 0x7D '}'
  {  9425,  22,   8,  27,    5,  -14 } }; // 0x7E '~'

const GFXfont FreeSansBoldOblique24pt7b PROGMEM = {
  (uint8_t  *)FreeSansBoldOblique24pt7bBitmaps,
  (GFXglyph *)FreeSansBoldOblique24pt7bGlyphs,
  0x20, 0x7E, 56 };

// Approx. 10119 bytes

const uint8_t FreeSansBold9pt7bBitmaps[] PROGMEM = {
  0xFF, 0xFF, 0xFE, 0x48, 0x7E, 0xEF, 0xDF, 0xBF, 0x74, 0x40, 0x19, 0x86,
  0x67, 0xFD, 0xFF, 0x33, 0x0C, 0xC3, 0x33, 0xFE, 0xFF, 0x99, 0x86, 0x61,
  0x90, 0x10, 0x1F, 0x1F, 0xDE, 0xFF, 0x3F, 0x83, 0xC0, 0xFC, 0x1F, 0x09,
  0xFC, 0xFE, 0xF7, 0xF1, 0xE0, 0x40, 0x38, 0x10, 0x7C, 0x30, 0xC6, 0x20,
  0xC6, 0x40, 0xC6, 0x40, 0x7C, 0x80, 0x39, 0x9C, 0x01, 0x3E, 0x03, 0x63,
  0x02, 0x63, 0x04, 0x63, 0x0C, 0x3E, 0x08, 0x1C, 0x0E, 0x01, 0xF8, 0x3B,
  0x83, 0xB8, 0x3F, 0x01, 0xE0, 0x3E, 0x67, 0x76, 0xE3, 0xEE, 0x1C, 0xF3,
  0xC7, 0xFE, 0x3F, 0x70, 0xFF, 0xF4, 0x18, 0x63, 0x1C, 0x73, 0x8E, 0x38,
  0xE3, 0x8E, 0x18, 0x70, 0xC3, 0x06, 0x08, 0x61, 0x83, 0x0E, 0x38, 0x71,
  0xC7, 0x1C, 0x71, 0xC6, 0x38, 0xE3, 0x18, 0x40, 0x21, 0x3E, 0x45, 0x28,
  0x38, 0x70, 0xE7, 0xFF, 0xE7, 0x0E, 0x1C, 0xFC, 0x9C, 0xFF, 0xC0, 0xFC,
  0x08, 0xC4, 0x23, 0x10, 0x84, 0x62, 0x11, 0x88, 0x00, 0x3E, 0x3F, 0x9D,
  0xDC, 0x7E, 0x3F, 0x1F, 0x8F, 0xC7, 0xE3, 0xF1, 0xDD, 0xCF, 0xE3, 0xE0,
  0x08, 0xFF, 0xF3, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x80, 0x3E, 0x3F, 0xB8,
  0xFC, 0x70, 0x38, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x1C, 0x0F, 0xF7, 0xF8,
  0x3C, 0x7F, 0xE7, 0xE7, 0x07, 0x0C, 0x0E, 0x07, 0x07, 0xE7, 0xE7, 0x7E,
  0x3C, 0x0E, 0x1E, 0x1E, 0x2E, 0x2E, 0x4E, 0x4E, 0x8E, 0xFF, 0xFF, 0x0E,
  0x0E, 0x0E, 0x7F, 0x3F, 0x90, 0x18, 0x0D, 0xE7, 0xFB, 0x9E, 0x07, 0x03,
  0x81, 0xF1, 0xFF, 0xE7, 0xC0, 0x3E, 0x3F, 0x9C, 0xFC, 0x0E, 0xE7, 0xFB,
  0xDF, 0xC7, 0xE3, 0xF1, 0xDD, 0xEF, 0xE3, 0xE0, 0xFF, 0xFF, 0xC0, 0xE0,
  0xE0, 0x60, 0x70, 0x30, 0x38, 0x1C, 0x0C, 0x0E, 0x07, 0x03, 0x80, 0x3F,
  0x1F, 0xEE, 0x3F, 0x87, 0xE3, 0xCF, 0xC7, 0xFB, 0xCF, 0xE1, 0xF8, 0x7F,
  0x3D, 0xFE, 0x3F, 0x00, 0x3E, 0x3F, 0xBD, 0xDC, 0x7E, 0x3F, 0x1F, 0xDE,
  0xFF, 0x3B, 0x81, 0xF9, 0xCF, 0xE3, 0xC0, 0xFC, 0x00, 0x07, 0xE0, 0xFC,
  0x00, 0x07, 0xE5, 0xE0, 0x00, 0x83, 0xC7, 0xDF, 0x0C, 0x07, 0x80, 0xF8,
  0x1F, 0x01, 0x80, 0xFF, 0xFF, 0xC0, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x70,
  0x3F, 0x03, 0xE0, 0x38, 0x7D, 0xF1, 0xE0, 0x80, 0x00, 0x3E, 0x3F, 0xB8,
  0xFC, 0x70, 0x38, 0x1C, 0x1C, 0x1C, 0x1C, 0x0E, 0x00, 0x03, 0x81, 0xC0,
  0x03, 0xF0, 0x0F, 0xFC, 0x1E, 0x0E, 0x38, 0x02, 0x70, 0xE9, 0x63, 0x19,
  0xC2, 0x19, 0xC6, 0x11, 0xC6, 0x33, 0xC6, 0x32, 0x63, 0xFE, 0x73, 0xDC,
  0x3C, 0x00, 0x1F, 0xF8, 0x07, 0xF0, 0x07, 0x00, 0xF0, 0x0F, 0x80, 0xF8,
  0x1D, 0x81, 0x9C, 0x19, 0xC3, 0x8C, 0x3F, 0xE7, 0xFE, 0x70, 0x66, 0x07,
  0xE0, 0x70, 0xFF, 0x9F, 0xFB, 0x83, 0xF0, 0x7E, 0x0F, 0xFF, 0x3F, 0xF7,
  0x06, 0xE0, 0xFC, 0x1F, 0x83, 0xFF, 0xEF, 0xF8, 0x1F, 0x83, 0xFE, 0x78,
  0xE7, 0x07, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x07, 0x07, 0x78,
  0xF3, 0xFE, 0x1F, 0x80, 0xFF, 0x8F, 0xFC, 0xE0, 0xEE, 0x0E, 0xE0, 0x7E,
  0x07, 0xE0, 0x7E, 0x07, 0xE0, 0x7E, 0x0E, 0xE0, 0xEF, 0xFC, 0xFF, 0x80,
  0xFF, 0xFF, 0xF8, 0x1C, 0x0E, 0x07, 0xFB, 0xFD, 0xC0, 0xE0, 0x70, 0x38,
  0x1F, 0xFF, 0xF8, 0xFF, 0xFF, 0xF8, 0x1C, 0x0E, 0x07, 0xFB, 0xFD, 0xC0,
  0xE0, 0x70, 0x38, 0x1C, 0x0E, 0x00, 0x0F, 0x87, 0xF9, 0xE3, 0xB8, 0x3E,
  0x01, 0xC0, 0x38, 0xFF, 0x1F, 0xE0, 0x6E, 0x0D, 0xE3, 0x9F, 0xD0, 0xF2,
  0xE0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xFF, 0xFF, 0xFF, 0x07, 0xE0,
  0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x07,
  0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0x07, 0xE7, 0xE7, 0xE7, 0x7E, 0x3C,
  0xE0, 0xEE, 0x1C, 0xE3, 0x8E, 0x70, 0xEE, 0x0F, 0xC0, 0xFE, 0x0F, 0x70,
  0xE7, 0x0E, 0x38, 0xE1, 0xCE, 0x0E, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0,
  0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xE0, 0xFF, 0xFF, 0xF8, 0x7F, 0xE1,
  0xFF, 0x87, 0xFE, 0x1F, 0xEC, 0x7F, 0xB3, 0x7E, 0xCD, 0xFB, 0x37, 0xEC,
  0xDF, 0x9E, 0x7E, 0x79, 0xF9, 0xE7, 0xE7, 0x9C, 0xE0, 0xFE, 0x1F, 0xC3,
  0xFC, 0x7F, 0xCF, 0xD9, 0xFB, 0xBF, 0x37, 0xE7, 0xFC, 0x7F, 0x87, 0xF0,
  0xFE, 0x0E, 0x0F, 0x81, 0xFF, 0x1E, 0x3C, 0xE0, 0xEE, 0x03, 0xF0, 0x1F,
  0x80, 0xFC, 0x07, 0xE0, 0x3B, 0x83, 0x9E, 0x3C, 0x7F, 0xC0, 0xF8, 0x00,
  0xFF, 0x9F, 0xFB, 0x87, 0xF0, 0x7E, 0x0F, 0xC3, 0xFF, 0xF7, 0xFC, 0xE0,
  0x1C, 0x03, 0x80, 0x70, 0x0E, 0x00, 0x0F, 0x81, 0xFF, 0x1E, 0x3C, 0xE0,
  0xEE, 0x03, 0xF0, 0x1F, 0x80, 0xFC, 0x07, 0xE1, 0xBB, 0x8F, 0x9E, 0x3C,
  0x7F, 0xE0, 0xFB, 0x80, 0x08, 0xFF, 0x8F, 0xFC, 0xE0, 0xEE, 0x0E, 0xE0,
  0xEE, 0x0E, 0xFF, 0xCF, 0xFC, 0xE0, 0xEE, 0x0E, 0xE0, 0xEE, 0x0E, 0xE0,
  0xF0, 0x3F, 0x0F, 0xFB, 0xC7, 0xF0, 0x7E, 0x01, 0xFC, 0x1F, 0xF0, 0x3F,
  0x00, 0xFC, 0x1D, 0xC7, 0xBF, 0xE1, 0xF8, 0xFF, 0xFF, 0xC7, 0x03, 0x81,
  0xC0, 0xE0, 0x70, 0x38, 0x1C, 0x0E, 0x07, 0x03, 0x81, 0xC0, 0xE0, 0xFC,
  0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07, 0xE0, 0xFC, 0x1F,
  0xC7, 0xBF, 0xE1, 0xF0, 0x60, 0x67, 0x0E, 0x70, 0xE3, 0x0C, 0x30, 0xC3,
  0x9C, 0x19, 0x81, 0x98, 0x1F, 0x80, 0xF0, 0x0F, 0x00, 0xF0, 0x06, 0x00,
  0x61, 0xC3, 0xB8, 0xE1, 0x9C, 0x70, 0xCE, 0x3C, 0xE3, 0x36, 0x71, 0x9B,
  0x30, 0xED, 0x98, 0x36, 0x7C, 0x1B, 0x3C, 0x0F, 0x1E, 0x07, 0x8F, 0x01,
  0xC3, 0x80, 0xE1, 0x80, 0x70, 0xE7, 0x8E, 0x39, 0xC1, 0xF8, 0x1F, 0x80,
  0xF0, 0x07, 0x00, 0xF0, 0x1F, 0x81, 0x9C, 0x39, 0xC7, 0x0E, 0x70, 0xE0,
  0xE0, 0xFC, 0x39, 0xC7, 0x18, 0xC3, 0xB8, 0x36, 0x07, 0xC0, 0x70, 0x0E,
  0x01, 0xC0, 0x38, 0x07, 0x00, 0xE0, 0xFF, 0xFF, 0xC0, 0xE0, 0xE0, 0xF0,
  0x70, 0x70, 0x70, 0x78, 0x38, 0x38, 0x1F, 0xFF, 0xF8, 0xFF, 0xEE, 0xEE,
  0xEE, 0xEE, 0xEE, 0xEE, 0xEF, 0xF0, 0x86, 0x10, 0x86, 0x10, 0x84, 0x30,
  0x84, 0x30, 0x80, 0xFF, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x7F, 0xF0,
  0x18, 0x1C, 0x3C, 0x3E, 0x36, 0x66, 0x63, 0xC3, 0xFF, 0xC0, 0xCC, 0x3F,
  0x1F, 0xEE, 0x38, 0x0E, 0x3F, 0x9E, 0xEE, 0x3B, 0x9E, 0xFF, 0x9E, 0xE0,
  0xE0, 0x38, 0x0E, 0x03, 0xBC, 0xFF, 0xBC, 0xEE, 0x1F, 0x87, 0xE1, 0xF8,
  0x7F, 0x3B, 0xFE, 0xEF, 0x00, 0x1F, 0x3F, 0xDC, 0x7C, 0x0E, 0x07, 0x03,
  0x80, 0xE3, 0x7F, 0x8F, 0x00, 0x03, 0x81, 0xC0, 0xE7, 0x77, 0xFB, 0xBF,
  0x8F, 0xC7, 0xE3, 0xF1, 0xFD, 0xEF, 0xF3, 0xB8, 0x3E, 0x3F, 0x9C, 0xDC,
  0x3F, 0xFF, 0xFF, 0x81, 0xC3, 0x7F, 0x8F, 0x00, 0x3B, 0xDD, 0xFF, 0xB9,
  0xCE, 0x73, 0x9C, 0xE7, 0x00, 0x3B, 0xBF, 0xDD, 0xFC, 0x7E, 0x3F, 0x1F,
  0x8F, 0xEF, 0x7F, 0x9D, 0xC0, 0xFC, 0x77, 0xF1, 0xF0, 0xE0, 0x70, 0x38,
  0x1D, 0xEF, 0xFF, 0x9F, 0x8F, 0xC7, 0xE3, 0xF1, 0xF8, 0xFC, 0x7E, 0x38,
  0xFC, 0x7F, 0xFF, 0xFF, 0xFE, 0x77, 0x07, 0x77, 0x77, 0x77, 0x77, 0x77,
  0x7F, 0xE0, 0xE0, 0x70, 0x38, 0x1C, 0x7E, 0x77, 0x73, 0xF1, 0xF8, 0xFE,
  0x77, 0x39, 0xDC, 0x6E, 0x38, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xEF, 0x7B,
  0xFF, 0xFE, 0x39, 0xF8, 0xE7, 0xE3, 0x9F, 0x8E, 0x7E, 0x39, 0xF8, 0xE7,
  0xE3, 0x9F, 0x8E, 0x70, 0xEF, 0x7F, 0xF8, 0xFC, 0x7E, 0x3F, 0x1F, 0x8F,
  0xC7, 0xE3, 0xF1, 0xC0, 0x1E, 0x1F, 0xE7, 0x3B, 0x87, 0xE1, 0xF8, 0x7E,
  0x1D, 0xCE, 0x7F, 0x87, 0x80, 0xEF, 0x3F, 0xEF, 0x3B, 0x87, 0xE1, 0xF8,
  0x7E, 0x1F, 0xCE, 0xFF, 0xBB, 0xCE, 0x03, 0x80, 0xE0, 0x38, 0x00, 0x3B,
  0xBF, 0xFD, 0xFC, 0x7E, 0x3F, 0x1F, 0x8F, 0xEF, 0x7F, 0x9D, 0xC0, 0xE0,
  0x70, 0x38, 0x1C, 0xEF, 0xFF, 0x38, 0xE3, 0x8E, 0x38, 0xE3, 0x80, 0x3E,
  0x3F, 0xB8, 0xFC, 0x0F, 0xC3, 0xFC, 0x3F, 0xC7, 0xFF, 0x1F, 0x00, 0x73,
  0xBF, 0xF7, 0x39, 0xCE, 0x73, 0x9E, 0x70, 0xE3, 0xF1, 0xF8, 0xFC, 0x7E,
  0x3F, 0x1F, 0x8F, 0xC7, 0xFF, 0xBD, 0xC0, 0xE1, 0x98, 0x67, 0x39, 0xCC,
  0x33, 0x0D, 0xC3, 0xE0, 0x78, 0x1E, 0x07, 0x00, 0xE3, 0x1D, 0x9E, 0x66,
  0x79, 0x99, 0xE6, 0x77, 0xB8, 0xD2, 0xC3, 0xCF, 0x0F, 0x3C, 0x3C, 0xF0,
  0x73, 0x80, 0x73, 0x9C, 0xE3, 0xF0, 0x78, 0x1E, 0x07, 0x81, 0xE0, 0xFC,
  0x73, 0x9C, 0xE0, 0xE1, 0xD8, 0x67, 0x39, 0xCE, 0x33, 0x0E, 0xC3, 0xE0,
  0x78, 0x1E, 0x03, 0x00, 0xC0, 0x70, 0x38, 0x0E, 0x00, 0xFE, 0xFE, 0x0E,
  0x1C, 0x38, 0x38, 0x70, 0xE0, 0xFF, 0xFF, 0x37, 0x66, 0x66, 0x6E, 0xE6,
  0x66, 0x66, 0x67, 0x30, 0xFF, 0xFF, 0x80, 0xCE, 0x66, 0x66, 0x67, 0x76,
  0x66, 0x66, 0x6E, 0xC0, 0x71, 0x8E };

const GFXglyph FreeSansBold9pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   5,    0,    1 },   // 0x20 ' '
  {     0,   3,  13,   6,    2,  -12 },   // 0x21 '!'
  {     5,   7,   5,   9,    1,  -12 },   // 0x22 '"'
  {    10,  10,  12,  10,    0,  -11 },   // 0x23 '#'
  {    25,   9,  15,  10,    1,  -13 },   // 0x24 '$'
  {    42,  16,  13,  16,    0,  -12 },   // 0x25 '%'
  {    68,  12,  13,  13,    1,  -12 },   // 0x26 '&'
  {    88,   3,   5,   5,    1,  -12 },   // 0x27 '''
  {    90,   6,  17,   6,    1,  -12 },   // 0x28 '('
  {   103,   6,  17,   6,    0,  -12 },   // 0x29 ')'
  {   116,   5,   6,   7,    1,  -12 },   // 0x2A '*'
  {   120,   7,   8,  11,    2,   -7 },   // 0x2B '+'
  {   127,   3,   5,   4,    1,   -1 },   // 0x2C ','
  {   129,   5,   2,   6,    0,   -5 },   // 0x2D '-'
  {   131,   3,   2,   4,    1,   -1 },   // 0x2E '.'
  {   132,   5,  13,   5,    0,  -12 },   // 0x2F '/'
  {   141,   9,  13,  10,    1,  -12 },   // 0x30 '0'
  {   156,   5,  13,  10,    2,  -12 },   // 0x31 '1'
  {   165,   9,  13,  10,    1,  -12 },   // 0x32 '2'
  {   180,   8,  13,  10,    1,  -12 },   // 0x33 '3'
  {   193,   8,  13,  10,    2,  -12 },   // 0x34 '4'
  {   206,   9,  13,  10,    1,  -12 },   // 0x35 '5'
  {   221,   9,  13,  10,    1,  -12 },   // 0x36 '6'
  {   236,   9,  13,  10,    0,  -12 },   // 0x37 '7'
  {   251,  10,  13,  10,    0,  -12 },   // 0x38 '8'
  {   268,   9,  13,  10,    1,  -12 },   // 0x39 '9'
  {   283,   3,   9,   4,    1,   -8 },   // 0x3A ':'
  {   287,   3,  12,   4,    1,   -8 },   // 0x3B ';'
  {   292,   9,   9,  11,    1,   -8 },   // 0x3C '<'
  {   303,   9,   6,  11,    1,   -6 },   // 0x3D '='
  {   310,   9,   9,  11,    1,   -8 },   // 0x3E '>'
  {   321,   9,  13,  11,    1,  -12 },   // 0x3F '?'
  {   336,  16,  15,  18,    0,  -12 },   // 0x40 '@'
  {   366,  12,  13,  13,    0,  -12 },   // 0x41 'A'
  {   386,  11,  13,  13,    1,  -12 },   // 0x42 'B'
  {   404,  12,  13,  13,    1,  -12 },   // 0x43 'C'
  {   424,  12,  13,  13,    1,  -12 },   // 0x44 'D'
  {   444,   9,  13,  12,    1,  -12 },   // 0x45 'E'
  {   459,   9,  13,  11,    1,  -12 },   // 0x46 'F'
  {   474,  11,  13,  14,    1,  -12 },   // 0x47 'G'
  {   492,  11,  13,  13,    1,  -12 },   // 0x48 'H'
  {   510,   3,  13,   6,    1,  -12 },   // 0x49 'I'
  {   515,   8,  13,  10,    1,  -12 },   // 0x4A 'J'
  {   528,  12,  13,  13,    1,  -12 },   // 0x4B 'K'
  {   548,   8,  13,  11,    1,  -12 },   // 0x4C 'L'
  {   561,  14,  13,  16,    1,  -12 },   // 0x4D 'M'
  {   584,  11,  13,  14,    1,  -12 },   // 0x4E 'N'
  {   602,  13,  13,  14,    1,  -12 },   // 0x4F 'O'
  {   624,  11,  13,  12,    1,  -12 },   // 0x50 'P'
  {   642,  13,  14,  14,    1,  -12 },   // 0x51 'Q'
  {   665,  12,  13,  13,    1,  -12 },   // 0x52 'R'
  {   685,  11,  13,  12,    1,  -12 },   // 0x53 'S'
  {   703,   9,  13,  12,    2,  -12 },   // 0x54 'T'
  {   718,  11,  13,  13,    1,  -12 },   // 0x55 'U'
  {   736,  12,  13,  12,    0,  -12 },   // 0x56 'V'
  {   756,  17,  13,  17,    0,  -12 },   // 0x57 'W'
  {   784,  12,  13,  12,    0,  -12 },   // 0x58 'X'
  {   804,  11,  13,  12,    1,  -12 },   // 0x59 'Y'
  {   822,   9,  13,  11,    1,  -12 },   // 0x5A 'Z'
  {   837,   4,  17,   6,    1,  -12 },   // 0x5B '['
  {   846,   5,  13,   5,    0,  -12 },   // 0x5C '\'
  {   855,   4,  17,   6,    0,  -12 },   // 0x5D ']'
  {   864,   8,   8,  11,    1,  -12 },   // 0x5E '^'
  {   872,  10,   1,  10,    0,    4 },   // 0x5F '_'
  {   874,   3,   2,   5,    0,  -12 },   // 0x60 '`'
  {   875,  10,  10,  10,    1,   -9 },   // 0x61 'a'
  {   888,  10,  13,  11,    1,  -12 },   // 0x62 'b'
  {   905,   9,  10,  10,    1,   -9 },   // 0x63 'c'
  {   917,   9,  13,  11,    1,  -12 },   // 0x64 'd'
  {   932,   9,  10,  10,    1,   -9 },   // 0x65 'e'
  {   944,   5,  13,   6,    1,  -12 },   // 0x66 'f'
  {   953,   9,  14,  11,    1,   -9 },   // 0x67 'g'
  {   969,   9,  13,  11,    1,  -12 },   // 0x68 'h'
  {   984,   3,  13,   5,    1,  -12 },   // 0x69 'i'
  {   989,   4,  17,   5,    0,  -12 },   // 0x6A 'j'
  {   998,   9,  13,  10,    1,  -12 },   // 0x6B 'k'
  {  1013,   3,  13,   5,    1,  -12 },   // 0x6C 'l'
  {  1018,  14,  10,  16,    1,   -9 },   // 0x6D 'm'
  {  1036,   9,  10,  11,    1,   -9 },   // 0x6E 'n'
  {  1048,  10,  10,  11,    1,   -9 },   // 0x6F 'o'
  {  1061,  10,  14,  11,    1,   -9 },   // 0x70 'p'
  {  1079,   9,  14,  11,    1,   -9 },   // 0x71 'q'
  {  1095,   6,  10,   7,    1,   -9 },   // 0x72 'r'
  {  1103,   9,  10,  10,    1,   -9 },   // 0x73 's'
  {  1115,   5,  12,   6,    1,  -11 },   // 0x74 't'
  {  1123,   9,  10,  11,    1,   -9 },   // 0x75 'u'
  {  1135,  10,  10,  10,    0,   -9 },   // 0x76 'v'
  {  1148,  14,  10,  14,    0,   -9 },   // 0x77 'w'
  {  1166,  10,  10,  10,    0,   -9 },   // 0x78 'x'
  {  1179,  10,  14,  10,    0,   -9 },   // 0x79 'y'
  {  1197,   8,  10,   9,    1,   -9 },   // 0x7A 'z'
  {  1207,   4,  17,   7,    1,  -12 },   // 0x7B '{'
  {  1216,   1,  17,   5,    2,  -12 },   // 0x7C '|'
  {  1219,   4,  17,   7,    2,  -12 },   // 0x7D '}'
  {  1228,   8,   2,   9,    0,   -4 } }; // 0x7E '~'

const GFXfont FreeSansBold9pt7b PROGMEM = {
  (uint8_t  *)FreeSansBold9pt7bBitmaps,
  (GFXglyph *)FreeSansBold9pt7bGlyphs,
  0x20, 0x7E, 22 };

// Approx. 1902 bytes

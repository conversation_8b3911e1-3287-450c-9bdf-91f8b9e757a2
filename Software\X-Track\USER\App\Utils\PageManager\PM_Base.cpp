/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#include "PageManager.h"
#include "PM_Log.h"
#include <algorithm>

#define PM_EMPTY_PAGE_NAME "EMPTY_PAGE"

/**
  * @brief  页面管理器构造函数
  * @param  factory: 页面工厂指针
  * @retval None
  */
PageManager::PageManager(PageFactory* factory)
    : _Factory(factory)
    , _PagePrev(nullptr)
    , _PageCurrent(nullptr)
    , _RootDefaultStyle(nullptr)
{
    memset(&_AnimState, 0, sizeof(_AnimState));

    SetGlobalLoadAnimType();
}

/**
  * @brief  页面管理器析构函数
  * @param  None
  * @retval None
  */
PageManager::~PageManager()
{
    SetStackClear();
}

/**
  * @brief  在页面池中搜索页面
  * @param  name: 页面名称
  * @retval 页面基类指针，如果未找到则返回nullptr
  */
PageBase* PageManager::FindPageInPool(const char* name)
{
    for (auto iter : _PagePool)
    {
        if (strcmp(name, iter->_Name) == 0)
        {
            return iter;
        }
    }
    return nullptr;
}

/**
  * @brief  在页面堆栈中搜索页面
  * @param  name: 页面名称
  * @retval 页面基类指针，如果未找到则返回nullptr
  */
PageBase* PageManager::FindPageInStack(const char* name)
{
    decltype(_PageStack) stk = _PageStack;
    while (!stk.empty())
    {
        PageBase* base = stk.top();

        if (strcmp(name, base->_Name) == 0)
        {
            return base;
        }

        stk.pop();
    }

    return nullptr;
}

/**
  * @brief  安装页面，并将页面注册到页面池
  * @param  className: 页面的类名
  * @param  appName: 页面应用名称，不允许重复
  * @retval 成功返回true
  */
bool PageManager::Install(const char* className, const char* appName)
{
    if (_Factory == nullptr)
    {
        PM_LOG_ERROR("Factory was not registered, can't install page");
        return false;
    }

    if (appName == nullptr)
    {
        PM_LOG_WARN("appName has not set");
        appName = className;
    }

    if (FindPageInPool(appName) != nullptr)
    {
        PM_LOG_ERROR("Page(%s) was registered", appName);
        return false;
    }

    PageBase* base = _Factory->CreatePage(className);
    if (base == nullptr)
    {
        PM_LOG_ERROR("Factory has not %s", className);
        return false;
    }

    base->_root = nullptr;
    base->_ID = 0;
    base->_Manager = nullptr;
    base->_UserData = nullptr;
    memset(&base->priv, 0, sizeof(base->priv));

    PM_LOG_INFO("Install Page[class = %s, name = %s]", className, appName);
    bool retval = Register(base, appName);

    base->onCustomAttrConfig();

    return retval;
}

/**
  * @brief  卸载页面
  * @param  appName: 页面应用名称，不允许重复
  * @retval 卸载成功返回true
  */
bool PageManager::Uninstall(const char* appName)
{
    PM_LOG_INFO("Page(%s) uninstall...", appName);

    PageBase* base = FindPageInPool(appName);
    if (base == nullptr)
    {
        PM_LOG_ERROR("Page(%s) was not found", appName);
        return false;
    }

    if (!Unregister(appName))
    {
        PM_LOG_ERROR("Page(%s) unregister failed", appName);
        return false;
    }

    if (base->priv.IsCached)
    {
        PM_LOG_WARN("Page(%s) has cached, unloading...", appName);
        base->priv.State = PageBase::PAGE_STATE_UNLOAD;
        StateUpdate(base);
    }
    else
    {
        PM_LOG_INFO("Page(%s) has not cache", appName);
    }

    delete base;
    PM_LOG_INFO("Uninstall OK");
    return true;
}

/**
  * @brief  将页面注册到页面池
  * @param  base: 页面基类指针
  * @param  name: 页面应用名称，不允许重复注册
  * @retval 注册成功返回true
  */
bool PageManager::Register(PageBase* base, const char* name)
{
    if (FindPageInPool(name) != nullptr)
    {
        PM_LOG_ERROR("Page(%s) was multi registered", name);
        return false;
    }

    base->_Manager = this;
    base->_Name = name;

    _PagePool.push_back(base);

    return true;
}

/**
  * @brief  从页面池中注销页面
  * @param  name: 页面应用名称
  * @retval 注销成功返回true
  */
bool PageManager::Unregister(const char* name)
{
    PM_LOG_INFO("Page(%s) unregister...", name);

    PageBase* base = FindPageInStack(name);

    if (base != nullptr)
    {
        PM_LOG_ERROR("Page(%s) was in stack", name);
        return false;
    }

    base = FindPageInPool(name);
    if (base == nullptr)
    {
        PM_LOG_ERROR("Page(%s) was not found", name);
        return false;
    }

    auto iter = std::find(_PagePool.begin(), _PagePool.end(), base);

    if (iter == _PagePool.end())
    {
        PM_LOG_ERROR("Page(%s) was not found in PagePool", name);
        return false;
    }

    _PagePool.erase(iter);

    PM_LOG_INFO("Unregister OK");
    return true;
}

/**
  * @brief  获取页面堆栈的顶部页面
  * @param  None
  * @retval 页面基类指针
  */
PageBase* PageManager::GetStackTop()
{
    return _PageStack.empty() ? nullptr : _PageStack.top();
}

/**
  * @brief  获取页面堆栈顶部下方的页面
  * @param  None
  * @retval 页面基类指针
  */
PageBase* PageManager::GetStackTopAfter()
{
    PageBase* top = GetStackTop();

    if (top == nullptr)
    {
        return nullptr;
    }

    _PageStack.pop();

    PageBase* topAfter = GetStackTop();

    _PageStack.push(top);

    return topAfter;
}

/**
  * @brief  清空页面堆栈并结束页面堆栈中所有页面的生命周期
  * @param  keepBottom: 是否保留堆栈底部页面
  * @retval None
  */
void PageManager::SetStackClear(bool keepBottom)
{
    while (1)
    {
        PageBase* top = GetStackTop();

        if (top == nullptr)
        {
            PM_LOG_INFO("Page stack is empty, breaking...");
            break;
        }

        PageBase* topAfter = GetStackTopAfter();

        if (topAfter == nullptr)
        {
            if (keepBottom)
            {
                _PagePrev = top;
                PM_LOG_INFO("Keep page stack bottom(%s), breaking...", top->_Name);
                break;
            }
            else
            {
                _PagePrev = nullptr;
            }
        }

        FourceUnload(top);

        _PageStack.pop();
    }
    PM_LOG_INFO("Stack clear done");
}

/**
  * @brief  获取前一页面的名称
  * @param  None
  * @retval 前一页面的名称，如果不存在则返回PM_EMPTY_PAGE_NAME
  */
const char* PageManager::GetPagePrevName()
{
    return _PagePrev ? _PagePrev->_Name : PM_EMPTY_PAGE_NAME;
}

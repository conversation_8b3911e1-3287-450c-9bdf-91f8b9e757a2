const uint8_t FreeSerifBoldItalic24pt7bBitmaps[] PROGMEM = {
  0x00, 0x3C, 0x00, 0xFC, 0x01, 0xF8, 0x07, 0xF0, 0x0F, 0xE0, 0x1F, 0xC0,
  0x3F, 0x00, 0x7E, 0x00, 0xF8, 0x01, 0xF0, 0x07, 0xC0, 0x0F, 0x80, 0x1E,
  0x00, 0x3C, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x00, 0x0E, 0x00,
  0x18, 0x00, 0x30, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xF0, 0x03, 0xF0, 0x0F, 0xF0, 0x1F, 0xE0, 0x3F, 0xC0, 0x3F, 0x00,
  0x3C, 0x00, 0x1C, 0x01, 0xC7, 0xC0, 0x7D, 0xF8, 0x1F, 0xBF, 0x03, 0xF7,
  0xC0, 0x7C, 0xF8, 0x0F, 0x9E, 0x01, 0xE3, 0xC0, 0x3C, 0x70, 0x07, 0x1E,
  0x00, 0xE3, 0x80, 0x38, 0x70, 0x07, 0x0C, 0x00, 0xC0, 0x00, 0x03, 0xC1,
  0xE0, 0x00, 0x70, 0x38, 0x00, 0x1E, 0x0F, 0x00, 0x03, 0xC1, 0xE0, 0x00,
  0x70, 0x38, 0x00, 0x1E, 0x0F, 0x00, 0x03, 0x81, 0xC0, 0x00, 0xF0, 0x78,
  0x00, 0x1E, 0x0F, 0x00, 0x07, 0x83, 0xC0, 0x1F, 0xFF, 0xFF, 0x83, 0xFF,
  0xFF, 0xF0, 0x7F, 0xFF, 0xFC, 0x00, 0xE0, 0x70, 0x00, 0x3C, 0x1E, 0x00,
  0x07, 0x83, 0xC0, 0x00, 0xE0, 0x70, 0x00, 0x3C, 0x1E, 0x00, 0x07, 0x83,
  0xC0, 0x00, 0xE0, 0x70, 0x07, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFC, 0x1F,
  0xFF, 0xFF, 0x00, 0x38, 0x1C, 0x00, 0x0F, 0x07, 0x80, 0x01, 0xE0, 0xF0,
  0x00, 0x38, 0x1C, 0x00, 0x0F, 0x07, 0x80, 0x01, 0xC0, 0xE0, 0x00, 0x78,
  0x3C, 0x00, 0x0F, 0x07, 0x80, 0x01, 0xC0, 0xE0, 0x00, 0x78, 0x3C, 0x00,
  0x00, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x1F,
  0xE0, 0x00, 0x7F, 0xF8, 0x01, 0xF1, 0x9E, 0x01, 0xC1, 0x8F, 0x03, 0x83,
  0x8F, 0x03, 0x83, 0x06, 0x07, 0x83, 0x06, 0x07, 0x87, 0x06, 0x07, 0xC7,
  0x04, 0x07, 0xE6, 0x04, 0x07, 0xFE, 0x00, 0x03, 0xFE, 0x00, 0x03, 0xFF,
  0x00, 0x01, 0xFF, 0x80, 0x00, 0xFF, 0xC0, 0x00, 0x7F, 0xE0, 0x00, 0x1F,
  0xE0, 0x00, 0x1F, 0xF0, 0x00, 0x3F, 0xF0, 0x00, 0x3B, 0xF8, 0x20, 0x31,
  0xF8, 0x20, 0x30, 0xF8, 0x60, 0x70, 0xF8, 0x60, 0x60, 0xF8, 0x60, 0x60,
  0xF8, 0xF0, 0xE0, 0xF0, 0xF0, 0xE1, 0xE0, 0x78, 0xC3, 0xE0, 0x3C, 0xC7,
  0xC0, 0x0F, 0xFF, 0x00, 0x03, 0xFC, 0x00, 0x01, 0x80, 0x00, 0x03, 0x80,
  0x00, 0x03, 0x80, 0x00, 0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0x01, 0xF0,
  0x00, 0x70, 0x00, 0xFF, 0x80, 0x1C, 0x00, 0x3F, 0x38, 0x1F, 0x00, 0x0F,
  0xC7, 0xFF, 0xE0, 0x03, 0xF0, 0x3F, 0xB8, 0x00, 0x7E, 0x04, 0x07, 0x00,
  0x1F, 0x80, 0x81, 0xC0, 0x03, 0xF0, 0x10, 0x38, 0x00, 0xFC, 0x02, 0x0E,
  0x00, 0x1F, 0x80, 0x81, 0x80, 0x03, 0xF0, 0x10, 0x70, 0x00, 0x7C, 0x06,
  0x1C, 0x00, 0x0F, 0x80, 0x83, 0x80, 0x01, 0xF0, 0x30, 0xE0, 0x00, 0x1E,
  0x0C, 0x1C, 0x07, 0xC3, 0xE3, 0x07, 0x03, 0xFC, 0x3F, 0xC0, 0xC0, 0xFC,
  0x43, 0xE0, 0x38, 0x3E, 0x0C, 0x00, 0x0E, 0x0F, 0xC0, 0x80, 0x01, 0xC3,
  0xF0, 0x10, 0x00, 0x70, 0xFC, 0x02, 0x00, 0x0C, 0x1F, 0x80, 0x40, 0x03,
  0x83, 0xE0, 0x08, 0x00, 0x60, 0xFC, 0x02, 0x00, 0x1C, 0x1F, 0x80, 0x40,
  0x07, 0x03, 0xE0, 0x10, 0x00, 0xE0, 0x7C, 0x02, 0x00, 0x38, 0x0F, 0x80,
  0xC0, 0x06, 0x01, 0xF0, 0x30, 0x01, 0xC0, 0x1F, 0x0C, 0x00, 0x30, 0x01,
  0xFF, 0x00, 0x0E, 0x00, 0x1F, 0x80, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x01, 0xF1, 0xE0, 0x00, 0x00, 0xF0, 0x78, 0x00, 0x00,
  0xF0, 0x3C, 0x00, 0x00, 0x78, 0x1E, 0x00, 0x00, 0x7C, 0x0F, 0x00, 0x00,
  0x3E, 0x0F, 0x80, 0x00, 0x1F, 0x07, 0x80, 0x00, 0x0F, 0x87, 0x80, 0x00,
  0x07, 0xC7, 0x80, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00,
  0x00, 0xFC, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0x07, 0xFE,
  0x03, 0xCF, 0xC0, 0xFE, 0x03, 0xC7, 0xE0, 0x3C, 0x07, 0xC3, 0xF0, 0x1C,
  0x07, 0xC0, 0xFC, 0x0C, 0x03, 0xC0, 0x7E, 0x0E, 0x03, 0xE0, 0x3F, 0x0E,
  0x01, 0xF0, 0x1F, 0xC6, 0x01, 0xF8, 0x07, 0xF6, 0x00, 0xFC, 0x03, 0xFF,
  0x00, 0x7E, 0x00, 0xFF, 0x00, 0x3F, 0x80, 0x7F, 0x80, 0x1F, 0xC0, 0x1F,
  0xC0, 0x07, 0xF0, 0x0F, 0xF0, 0x13, 0xFE, 0x0F, 0xFE, 0x18, 0xFF, 0xFE,
  0xFF, 0xF8, 0x3F, 0xFE, 0x3F, 0xF8, 0x07, 0xF8, 0x03, 0xF0, 0x00, 0x1C,
  0x7D, 0xFB, 0xF7, 0xCF, 0x9E, 0x3C, 0x71, 0xE3, 0x87, 0x0C, 0x00, 0x00,
  0x04, 0x00, 0x70, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x3C, 0x01,
  0xE0, 0x0F, 0x00, 0x3C, 0x01, 0xE0, 0x0F, 0x80, 0x3C, 0x00, 0xF0, 0x07,
  0xC0, 0x1E, 0x00, 0x78, 0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0xF0, 0x03,
  0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00,
  0x70, 0x01, 0xC0, 0x07, 0x00, 0x1C, 0x00, 0x30, 0x00, 0xE0, 0x01, 0x80,
  0x06, 0x00, 0x0C, 0x00, 0x30, 0x00, 0x60, 0x01, 0x80, 0x00, 0x00, 0x01,
  0x00, 0x06, 0x00, 0x08, 0x00, 0x30, 0x00, 0x40, 0x01, 0x80, 0x06, 0x00,
  0x1C, 0x00, 0x30, 0x00, 0xE0, 0x03, 0x80, 0x0E, 0x00, 0x38, 0x00, 0xF0,
  0x03, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x7C,
  0x01, 0xF0, 0x07, 0xC0, 0x1E, 0x00, 0x78, 0x03, 0xE0, 0x0F, 0x80, 0x3C,
  0x01, 0xF0, 0x07, 0x80, 0x1E, 0x00, 0xF0, 0x03, 0x80, 0x1E, 0x00, 0xF0,
  0x03, 0x80, 0x1C, 0x00, 0xE0, 0x06, 0x00, 0x30, 0x00, 0x80, 0x00, 0x00,
  0xE0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0xF8, 0x07, 0x0E, 0x1D, 0xF1,
  0xC7, 0xFF, 0x11, 0xFF, 0xE2, 0x3F, 0x7E, 0x4F, 0xC0, 0x3E, 0x00, 0x07,
  0xC0, 0x3F, 0x27, 0xEF, 0xC4, 0x7F, 0xF8, 0x8F, 0xFE, 0x38, 0xFB, 0x87,
  0x0E, 0x01, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0x70, 0x00, 0x00,
  0x78, 0x00, 0x01, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x78,
  0x00, 0x01, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x03,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x01,
  0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0,
  0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00,
  0x07, 0x80, 0x00, 0x0F, 0x07, 0xE1, 0xFC, 0x7F, 0x1F, 0xC3, 0xF0, 0x7C,
  0x0E, 0x03, 0x80, 0xC0, 0x60, 0x30, 0x18, 0x1C, 0x04, 0x00, 0x7F, 0xF7,
  0xFF, 0x7F, 0xEF, 0xFE, 0xFF, 0xE0, 0x3C, 0x7E, 0xFF, 0xFF, 0xFF, 0x7E,
  0x3C, 0x00, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00,
  0x78, 0x00, 0x1E, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x07,
  0xC0, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x00, 0xF0, 0x00, 0x3C,
  0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x01, 0xE0,
  0x00, 0x7C, 0x00, 0x0F, 0x00, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x0F, 0x00,
  0x03, 0xC0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x03, 0xC0, 0x00, 0xF8, 0x00,
  0x1E, 0x00, 0x07, 0xC0, 0x00, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0xE3, 0x80, 0x0F, 0x07, 0x00, 0x7C, 0x1C, 0x03, 0xE0, 0x78, 0x0F, 0x81,
  0xE0, 0x7C, 0x07, 0x83, 0xF0, 0x1F, 0x0F, 0xC0, 0xFC, 0x7E, 0x03, 0xF1,
  0xF8, 0x0F, 0xCF, 0xE0, 0x3F, 0x3F, 0x00, 0xFD, 0xFC, 0x07, 0xF7, 0xF0,
  0x1F, 0xDF, 0xC0, 0x7F, 0x7E, 0x01, 0xFB, 0xF8, 0x0F, 0xEF, 0xE0, 0x3F,
  0xBF, 0x80, 0xFE, 0xFC, 0x03, 0xF3, 0xF0, 0x1F, 0xCF, 0xC0, 0x7F, 0x3F,
  0x01, 0xF8, 0xFC, 0x07, 0xE3, 0xE0, 0x3F, 0x0F, 0x80, 0xFC, 0x1E, 0x07,
  0xE0, 0x78, 0x1F, 0x00, 0xE0, 0x78, 0x03, 0x83, 0xC0, 0x07, 0x1E, 0x00,
  0x07, 0xE0, 0x00, 0x00, 0x00, 0x70, 0x01, 0xFE, 0x01, 0xFF, 0xE0, 0x00,
  0xFE, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x0F, 0xC0, 0x01, 0xFC, 0x00,
  0x1F, 0x80, 0x01, 0xF8, 0x00, 0x3F, 0x80, 0x03, 0xF8, 0x00, 0x3F, 0x00,
  0x03, 0xF0, 0x00, 0x7F, 0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0x07, 0xE0,
  0x00, 0xFE, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x1F, 0xC0, 0x01, 0xFC,
  0x00, 0x1F, 0x80, 0x01, 0xF8, 0x00, 0x3F, 0x80, 0x03, 0xF0, 0x00, 0x3F,
  0x00, 0x07, 0xF0, 0x00, 0x7F, 0x00, 0x1F, 0xF8, 0x0F, 0xFF, 0xF0, 0x00,
  0x0F, 0x80, 0x01, 0xFF, 0x80, 0x0F, 0xFF, 0x00, 0x7F, 0xFE, 0x03, 0x83,
  0xF8, 0x0C, 0x07, 0xF0, 0x60, 0x1F, 0xC3, 0x00, 0x3F, 0x00, 0x00, 0xFC,
  0x00, 0x03, 0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3E, 0x00, 0x01, 0xF8, 0x00,
  0x07, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xC0, 0x00, 0x1E,
  0x00, 0x00, 0xF0, 0x00, 0x07, 0x80, 0x00, 0x3C, 0x00, 0x01, 0xE0, 0x00,
  0x0E, 0x00, 0x00, 0x70, 0x06, 0x03, 0x80, 0x10, 0x1C, 0x00, 0xC0, 0xE0,
  0x06, 0x07, 0xFF, 0xF8, 0x3F, 0xFF, 0xE1, 0xFF, 0xFF, 0x0F, 0xFF, 0xFC,
  0x3F, 0xFF, 0xE0, 0x00, 0x0F, 0xC0, 0x00, 0xFF, 0xC0, 0x0F, 0xFF, 0x80,
  0x60, 0xFE, 0x03, 0x01, 0xFC, 0x08, 0x03, 0xF0, 0x00, 0x0F, 0xC0, 0x00,
  0x3F, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0xFC,
  0x00, 0x07, 0xC0, 0x00, 0x3E, 0x00, 0x07, 0xF8, 0x00, 0x7F, 0xF0, 0x00,
  0x7F, 0xE0, 0x00, 0x3F, 0xC0, 0x00, 0x7F, 0x00, 0x01, 0xFC, 0x00, 0x03,
  0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xE0,
  0x00, 0x0F, 0x80, 0x00, 0x3C, 0x1C, 0x01, 0xF0, 0xF8, 0x07, 0x83, 0xF0,
  0x3C, 0x0F, 0xE1, 0xE0, 0x1F, 0xFE, 0x00, 0x1F, 0xC0, 0x00, 0x00, 0x00,
  0x07, 0x00, 0x00, 0x07, 0x80, 0x00, 0x07, 0xC0, 0x00, 0x07, 0xE0, 0x00,
  0x07, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xF8, 0x00, 0x07, 0xFC, 0x00,
  0x06, 0xFC, 0x00, 0x06, 0x7E, 0x00, 0x06, 0x3F, 0x00, 0x06, 0x3F, 0x00,
  0x06, 0x1F, 0x80, 0x06, 0x0F, 0xC0, 0x06, 0x07, 0xE0, 0x03, 0x07, 0xE0,
  0x03, 0x03, 0xF0, 0x03, 0x01, 0xF8, 0x03, 0x01, 0xFC, 0x03, 0x00, 0xFC,
  0x03, 0x00, 0x7E, 0x03, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF,
  0xF0, 0xFF, 0xFF, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0x07, 0xE0, 0x00, 0x03,
  0xF0, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xFC, 0x00, 0x00, 0xFC, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x7F, 0xFE, 0x00, 0x7F, 0xFE, 0x00,
  0x7F, 0xFC, 0x00, 0xFF, 0xFC, 0x00, 0xC0, 0x00, 0x01, 0x80, 0x00, 0x01,
  0x80, 0x00, 0x03, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x07, 0xFE, 0x00, 0x07,
  0xFF, 0x00, 0x07, 0xFF, 0x80, 0x0F, 0xFF, 0xC0, 0x00, 0xFF, 0xE0, 0x00,
  0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xF0, 0x00,
  0x03, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00,
  0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x03, 0xC0, 0x78, 0x03, 0xC0, 0xFC,
  0x07, 0x80, 0xFC, 0x0F, 0x00, 0xFE, 0x1E, 0x00, 0x7F, 0xF8, 0x00, 0x1F,
  0xC0, 0x00, 0x00, 0x00, 0x0E, 0x00, 0x01, 0xF8, 0x00, 0x0F, 0x80, 0x00,
  0x7E, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x01, 0xFC,
  0x00, 0x03, 0xF0, 0x00, 0x0F, 0xE0, 0x00, 0x3F, 0x80, 0x00, 0xFE, 0x00,
  0x01, 0xFF, 0xF0, 0x07, 0xFF, 0xF0, 0x0F, 0xE1, 0xF0, 0x3F, 0x81, 0xF0,
  0x7F, 0x03, 0xF0, 0xFC, 0x07, 0xE3, 0xF8, 0x0F, 0xC7, 0xF0, 0x1F, 0x8F,
  0xC0, 0x7F, 0x1F, 0x80, 0xFE, 0x3F, 0x01, 0xFC, 0x7C, 0x03, 0xF0, 0xF8,
  0x0F, 0xE1, 0xF0, 0x1F, 0xC1, 0xE0, 0x3F, 0x03, 0xC0, 0xFC, 0x07, 0x81,
  0xF0, 0x07, 0x87, 0xC0, 0x07, 0xFF, 0x00, 0x03, 0xF8, 0x00, 0x0F, 0xFF,
  0xFC, 0x1F, 0xFF, 0xF8, 0x3F, 0xFF, 0xE0, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF,
  0x07, 0x00, 0x1C, 0x08, 0x00, 0x78, 0x30, 0x01, 0xE0, 0x40, 0x03, 0xC0,
  0x00, 0x0F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00,
  0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01,
  0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x78,
  0x00, 0x01, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x1E, 0x00,
  0x00, 0x78, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x80, 0x00,
  0x1E, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x03, 0xFE, 0x00, 0x3C, 0x78,
  0x03, 0xC1, 0xE0, 0x3C, 0x07, 0x81, 0xE0, 0x3C, 0x1F, 0x01, 0xE0, 0xF8,
  0x0F, 0x07, 0xC0, 0x78, 0x3F, 0x03, 0xC1, 0xF8, 0x3C, 0x0F, 0xE1, 0xE0,
  0x3F, 0x9E, 0x01, 0xFF, 0xC0, 0x07, 0xFC, 0x00, 0x3F, 0xC0, 0x00, 0xFF,
  0x00, 0x1F, 0xFC, 0x03, 0xCF, 0xF0, 0x3C, 0x3F, 0x83, 0xC0, 0xFC, 0x3C,
  0x03, 0xF1, 0xE0, 0x1F, 0x9E, 0x00, 0x7C, 0xF0, 0x03, 0xE7, 0x80, 0x1F,
  0x3C, 0x00, 0xF9, 0xE0, 0x07, 0x87, 0x00, 0x3C, 0x3C, 0x03, 0xC0, 0xF0,
  0x3C, 0x03, 0xC3, 0xC0, 0x07, 0xF0, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0xFF,
  0xE0, 0x03, 0xF1, 0xE0, 0x0F, 0xC1, 0xC0, 0x3F, 0x03, 0xC0, 0xFE, 0x07,
  0x81, 0xF8, 0x0F, 0x87, 0xF0, 0x1F, 0x0F, 0xC0, 0x3E, 0x3F, 0x80, 0xFC,
  0x7F, 0x01, 0xF8, 0xFC, 0x03, 0xF1, 0xF8, 0x07, 0xE3, 0xF0, 0x1F, 0xC7,
  0xE0, 0x3F, 0x8F, 0xC0, 0x7E, 0x0F, 0x81, 0xFC, 0x1F, 0x03, 0xF8, 0x1F,
  0x0F, 0xE0, 0x1F, 0xFF, 0xC0, 0x1F, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x03,
  0xF8, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x7E, 0x00, 0x01, 0xF8,
  0x00, 0x07, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x7C, 0x00, 0x03, 0xE0, 0x00,
  0x1F, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x01, 0xE0, 0x1F, 0x81, 0xFE, 0x0F,
  0xF0, 0x7F, 0x81, 0xF8, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x7E, 0x07, 0xF8, 0x3F,
  0xC1, 0xFE, 0x07, 0xE0, 0x1E, 0x00, 0x00, 0x78, 0x01, 0xF8, 0x07, 0xF8,
  0x0F, 0xF0, 0x1F, 0xE0, 0x1F, 0x80, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80,
  0x1F, 0x80, 0x3F, 0x80, 0x7F, 0x00, 0xFE, 0x00, 0xFC, 0x00, 0xF8, 0x00,
  0xE0, 0x01, 0xC0, 0x07, 0x00, 0x0C, 0x00, 0x30, 0x01, 0xC0, 0x0E, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x07, 0x00, 0x00, 0x1F,
  0x00, 0x00, 0x7F, 0x00, 0x03, 0xFF, 0x00, 0x0F, 0xFC, 0x00, 0x3F, 0xF0,
  0x01, 0xFF, 0xC0, 0x07, 0xFE, 0x00, 0x1F, 0xF8, 0x00, 0x7F, 0xE0, 0x00,
  0xFF, 0x80, 0x00, 0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0xE0, 0x00,
  0x1F, 0xF8, 0x00, 0x07, 0xFE, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x3F, 0xF0,
  0x00, 0x0F, 0xFC, 0x00, 0x03, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x1F,
  0x00, 0x00, 0x07, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0xE0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xFF, 0x00, 0x00,
  0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0x00, 0x0F, 0xFC, 0x00, 0x03, 0xFF, 0x80,
  0x00, 0x7F, 0xE0, 0x00, 0x1F, 0xF8, 0x00, 0x07, 0xFF, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0x00, 0x03, 0xFF, 0x00, 0x1F, 0xFC,
  0x00, 0x7F, 0xE0, 0x01, 0xFF, 0x80, 0x0F, 0xFE, 0x00, 0x3F, 0xF0, 0x00,
  0xFF, 0xC0, 0x00, 0xFF, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xE0, 0x00, 0x00,
  0x80, 0x00, 0x00, 0x01, 0xF8, 0x01, 0xFF, 0x80, 0xF1, 0xF0, 0x38, 0x3E,
  0x1E, 0x0F, 0xC7, 0xC3, 0xF1, 0xF0, 0xFC, 0x7C, 0x3F, 0x0E, 0x0F, 0xC0,
  0x07, 0xF0, 0x01, 0xF8, 0x00, 0xFC, 0x00, 0x3F, 0x00, 0x1F, 0x00, 0x07,
  0x80, 0x03, 0xC0, 0x01, 0xE0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x0C, 0x00,
  0x06, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x03, 0xC0, 0x01, 0xF8, 0x00, 0xFF, 0x00, 0x3F, 0xC0, 0x0F, 0xF0,
  0x01, 0xF8, 0x00, 0x3C, 0x00, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x1F,
  0xFF, 0xC0, 0x00, 0x3F, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x7C,
  0x00, 0x03, 0x80, 0x7C, 0x00, 0x00, 0xE0, 0x7C, 0x00, 0x00, 0x38, 0x3C,
  0x00, 0xF0, 0x4C, 0x3E, 0x00, 0xFD, 0xE7, 0x1E, 0x00, 0xF3, 0xF1, 0x9F,
  0x00, 0xF1, 0xF0, 0xEF, 0x80, 0xF0, 0x78, 0x3F, 0x80, 0xF0, 0x3C, 0x1F,
  0xC0, 0x78, 0x1E, 0x0F, 0xE0, 0x78, 0x1E, 0x07, 0xF0, 0x3C, 0x0F, 0x03,
  0xF8, 0x3E, 0x07, 0x81, 0xFC, 0x1E, 0x07, 0x81, 0xFE, 0x0F, 0x03, 0xC0,
  0xDF, 0x07, 0x83, 0xC0, 0x6F, 0x83, 0xC3, 0xE0, 0x63, 0xE1, 0xF3, 0xF0,
  0x71, 0xF0, 0x7E, 0x78, 0x70, 0xF8, 0x1E, 0x3F, 0xF0, 0x3E, 0x00, 0x07,
  0xE0, 0x0F, 0x00, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x03, 0x80, 0x03, 0xF0,
  0x07, 0xC0, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x38, 0x00, 0x00,
  0x00, 0xF0, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00,
  0x1F, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x03,
  0x7E, 0x00, 0x00, 0x06, 0xFC, 0x00, 0x00, 0x19, 0xF8, 0x00, 0x00, 0x63,
  0xF8, 0x00, 0x00, 0xC7, 0xF0, 0x00, 0x03, 0x07, 0xE0, 0x00, 0x06, 0x0F,
  0xC0, 0x00, 0x18, 0x1F, 0x80, 0x00, 0x60, 0x3F, 0x00, 0x00, 0xC0, 0x7F,
  0x00, 0x03, 0x00, 0xFE, 0x00, 0x0F, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xF8,
  0x00, 0x60, 0x03, 0xF0, 0x00, 0xC0, 0x07, 0xE0, 0x03, 0x00, 0x0F, 0xE0,
  0x0E, 0x00, 0x1F, 0xC0, 0x18, 0x00, 0x3F, 0x80, 0x70, 0x00, 0x7F, 0x01,
  0xC0, 0x00, 0xFE, 0x03, 0x80, 0x01, 0xFE, 0x1F, 0x80, 0x07, 0xFE, 0x7F,
  0xC0, 0x3F, 0xFF, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xE0, 0x00,
  0xFE, 0x1F, 0xE0, 0x01, 0xFC, 0x1F, 0xE0, 0x03, 0xF8, 0x1F, 0xE0, 0x0F,
  0xE0, 0x3F, 0xC0, 0x1F, 0xC0, 0x7F, 0x80, 0x3F, 0x80, 0xFF, 0x00, 0x7F,
  0x01, 0xFE, 0x01, 0xFC, 0x03, 0xF8, 0x03, 0xF8, 0x0F, 0xF0, 0x07, 0xF0,
  0x1F, 0xC0, 0x0F, 0xC0, 0x7F, 0x00, 0x3F, 0x87, 0xF0, 0x00, 0x7F, 0xFF,
  0x00, 0x00, 0xFE, 0x1F, 0xC0, 0x03, 0xF8, 0x0F, 0xE0, 0x07, 0xF0, 0x0F,
  0xE0, 0x0F, 0xE0, 0x1F, 0xC0, 0x1F, 0xC0, 0x3F, 0xC0, 0x7F, 0x00, 0x7F,
  0x80, 0xFE, 0x00, 0xFF, 0x01, 0xFC, 0x01, 0xFE, 0x03, 0xF0, 0x07, 0xFC,
  0x0F, 0xE0, 0x0F, 0xF0, 0x1F, 0xC0, 0x3F, 0xE0, 0x3F, 0x80, 0x7F, 0x80,
  0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x0F, 0xF8, 0x07, 0xFF, 0xFF, 0xC0, 0x3F,
  0xFF, 0xFC, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x08, 0x00, 0x7F, 0xFE, 0xC0,
  0x0F, 0xF0, 0x7E, 0x00, 0xFE, 0x01, 0xF0, 0x1F, 0xE0, 0x07, 0x01, 0xFE,
  0x00, 0x38, 0x1F, 0xE0, 0x00, 0xC0, 0xFE, 0x00, 0x06, 0x0F, 0xF0, 0x00,
  0x30, 0xFF, 0x00, 0x01, 0x07, 0xF8, 0x00, 0x08, 0x7F, 0x80, 0x00, 0x03,
  0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x01, 0xFE, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x03,
  0xFC, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x1F, 0xE0, 0x00, 0x60, 0x7F, 0x00, 0x06, 0x03, 0xFC, 0x00,
  0x70, 0x0F, 0xE0, 0x07, 0x00, 0x1F, 0xC0, 0xE0, 0x00, 0x7F, 0xFE, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00, 0x3F, 0xFF, 0xFE,
  0x00, 0x00, 0xFE, 0x07, 0xF0, 0x00, 0x1F, 0xC0, 0x3F, 0x00, 0x03, 0xF8,
  0x07, 0xF0, 0x00, 0xFE, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x07, 0xF0, 0x03,
  0xF8, 0x00, 0xFE, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x1F, 0xC0, 0x03, 0xFC,
  0x03, 0xF8, 0x00, 0x7F, 0x80, 0x7F, 0x00, 0x0F, 0xF0, 0x0F, 0xC0, 0x01,
  0xFE, 0x03, 0xF8, 0x00, 0x3F, 0xC0, 0x7F, 0x00, 0x07, 0xF8, 0x0F, 0xE0,
  0x01, 0xFF, 0x03, 0xF8, 0x00, 0x3F, 0xE0, 0x7F, 0x00, 0x07, 0xF8, 0x0F,
  0xE0, 0x00, 0xFF, 0x01, 0xFC, 0x00, 0x3F, 0xE0, 0x7F, 0x00, 0x07, 0xF8,
  0x0F, 0xE0, 0x01, 0xFF, 0x01, 0xFC, 0x00, 0x3F, 0xC0, 0x3F, 0x00, 0x0F,
  0xF0, 0x0F, 0xE0, 0x01, 0xFC, 0x01, 0xFC, 0x00, 0x7F, 0x00, 0x3F, 0x80,
  0x1F, 0xC0, 0x0F, 0xE0, 0x0F, 0xF0, 0x01, 0xFE, 0x07, 0xF8, 0x00, 0x7F,
  0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0xFF,
  0x00, 0x7F, 0xFF, 0xFF, 0x00, 0x3F, 0xC0, 0x7E, 0x00, 0x3F, 0x80, 0x1E,
  0x00, 0x3F, 0x80, 0x0E, 0x00, 0x7F, 0x00, 0x06, 0x00, 0x7F, 0x00, 0x04,
  0x00, 0x7F, 0x00, 0x04, 0x00, 0x7F, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x80,
  0x00, 0xFE, 0x01, 0x00, 0x00, 0xFE, 0x03, 0x00, 0x00, 0xFC, 0x0F, 0x00,
  0x01, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x01, 0xFC, 0x3E, 0x00,
  0x03, 0xF8, 0x1E, 0x00, 0x03, 0xF8, 0x0C, 0x00, 0x03, 0xF8, 0x0C, 0x00,
  0x03, 0xF8, 0x0C, 0x00, 0x07, 0xF0, 0x08, 0x00, 0x07, 0xF0, 0x00, 0x08,
  0x07, 0xF0, 0x00, 0x18, 0x07, 0xE0, 0x00, 0x30, 0x0F, 0xE0, 0x00, 0x30,
  0x0F, 0xE0, 0x00, 0x70, 0x0F, 0xE0, 0x01, 0xE0, 0x1F, 0xC0, 0x07, 0xE0,
  0x1F, 0xE0, 0x3F, 0xE0, 0x3F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFF, 0xC0,
  0x01, 0xFF, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xFC, 0x00, 0xFF, 0x03, 0xF0,
  0x01, 0xFC, 0x01, 0xE0, 0x03, 0xF8, 0x01, 0xC0, 0x0F, 0xE0, 0x01, 0x80,
  0x1F, 0xC0, 0x02, 0x00, 0x3F, 0x80, 0x04, 0x00, 0x7F, 0x00, 0x00, 0x01,
  0xFC, 0x03, 0x00, 0x03, 0xF8, 0x04, 0x00, 0x07, 0xF0, 0x18, 0x00, 0x0F,
  0xC0, 0xF0, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0x80, 0x00, 0xFE,
  0x1F, 0x00, 0x03, 0xF8, 0x1E, 0x00, 0x07, 0xF0, 0x18, 0x00, 0x0F, 0xE0,
  0x30, 0x00, 0x1F, 0xC0, 0x60, 0x00, 0x7F, 0x00, 0x80, 0x00, 0xFE, 0x01,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x0F, 0xE0, 0x00,
  0x00, 0x1F, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0xFF, 0x00, 0x00,
  0x01, 0xFE, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x02, 0x00, 0x0F, 0xFF, 0xEE, 0x00, 0x3F, 0xC0,
  0xFC, 0x00, 0x7F, 0x00, 0x7C, 0x01, 0xFE, 0x00, 0x3C, 0x03, 0xFC, 0x00,
  0x38, 0x07, 0xF8, 0x00, 0x18, 0x07, 0xF0, 0x00, 0x18, 0x0F, 0xF0, 0x00,
  0x10, 0x1F, 0xE0, 0x00, 0x10, 0x1F, 0xE0, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x00, 0x3F, 0xC0, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x7F, 0x80, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0x80, 0x1F,
  0xFF, 0xFF, 0x00, 0x07, 0xFC, 0xFF, 0x00, 0x03, 0xF8, 0xFF, 0x00, 0x03,
  0xF8, 0xFF, 0x00, 0x03, 0xF0, 0xFF, 0x00, 0x03, 0xF0, 0xFF, 0x00, 0x07,
  0xF0, 0x7F, 0x00, 0x07, 0xF0, 0x7F, 0x00, 0x07, 0xE0, 0x7F, 0x80, 0x07,
  0xE0, 0x3F, 0x80, 0x0F, 0xE0, 0x1F, 0xC0, 0x0F, 0xC0, 0x0F, 0xE0, 0x0F,
  0xC0, 0x07, 0xF0, 0x3F, 0x80, 0x01, 0xFF, 0xFE, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x01, 0xFF, 0xFC, 0x7F, 0xFE, 0x00, 0xFF, 0xC0, 0x3F, 0xF0, 0x00,
  0xFE, 0x00, 0x3F, 0xC0, 0x01, 0xFC, 0x00, 0x7F, 0x00, 0x03, 0xF8, 0x00,
  0xFE, 0x00, 0x0F, 0xE0, 0x01, 0xFC, 0x00, 0x1F, 0xC0, 0x07, 0xF0, 0x00,
  0x3F, 0x80, 0x0F, 0xE0, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x01, 0xFC, 0x00,
  0x7F, 0x00, 0x03, 0xF8, 0x00, 0xFE, 0x00, 0x07, 0xF0, 0x01, 0xFC, 0x00,
  0x0F, 0xC0, 0x03, 0xF8, 0x00, 0x3F, 0x80, 0x0F, 0xE0, 0x00, 0x7F, 0xFF,
  0xFF, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0x80, 0x03, 0xF8, 0x00, 0x7F, 0x00,
  0x07, 0xF0, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x03, 0xF8, 0x00, 0x1F, 0xC0,
  0x07, 0xF0, 0x00, 0x7F, 0x00, 0x1F, 0xC0, 0x00, 0xFE, 0x00, 0x3F, 0x80,
  0x01, 0xFC, 0x00, 0x7F, 0x00, 0x03, 0xF0, 0x00, 0xFE, 0x00, 0x0F, 0xE0,
  0x03, 0xF8, 0x00, 0x1F, 0xC0, 0x07, 0xF0, 0x00, 0x3F, 0x80, 0x0F, 0xE0,
  0x00, 0xFF, 0x00, 0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x7F, 0x80, 0x07, 0xFC,
  0x01, 0xFF, 0x00, 0x3F, 0xFF, 0x1F, 0xFF, 0xC0, 0x00, 0x01, 0xFF, 0xF8,
  0x03, 0xFE, 0x00, 0x0F, 0xE0, 0x00, 0x7F, 0x00, 0x03, 0xF8, 0x00, 0x3F,
  0x80, 0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x00, 0x7E, 0x00, 0x07, 0xF0, 0x00,
  0x3F, 0x80, 0x01, 0xFC, 0x00, 0x0F, 0xC0, 0x00, 0xFE, 0x00, 0x07, 0xF0,
  0x00, 0x3F, 0x80, 0x03, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFE, 0x00, 0x07,
  0xE0, 0x00, 0x7F, 0x00, 0x03, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFC, 0x00,
  0x0F, 0xE0, 0x00, 0x7F, 0x00, 0x03, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xFC,
  0x00, 0x1F, 0xF0, 0x03, 0xFF, 0xF0, 0x00, 0x00, 0x07, 0xFF, 0xE0, 0x00,
  0x3F, 0xF0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x0F, 0xE0,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x0F, 0xE0, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F,
  0x80, 0x00, 0x07, 0xF0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x07, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0,
  0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xF0, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x07, 0x03, 0xF0, 0x01, 0xF0, 0xFE, 0x00,
  0x3E, 0x1F, 0xC0, 0x07, 0xC3, 0xF0, 0x00, 0xF8, 0xFC, 0x00, 0x0F, 0x3F,
  0x80, 0x00, 0xFF, 0xC0, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x01, 0xFF, 0xF8,
  0xFF, 0xC0, 0x1F, 0xF8, 0x0F, 0xC0, 0x03, 0xF8, 0x01, 0xC0, 0x00, 0xFE,
  0x00, 0xE0, 0x00, 0x3F, 0x80, 0x70, 0x00, 0x1F, 0xC0, 0x38, 0x00, 0x07,
  0xF0, 0x1C, 0x00, 0x01, 0xFC, 0x0E, 0x00, 0x00, 0x7F, 0x07, 0x00, 0x00,
  0x3F, 0x83, 0x80, 0x00, 0x0F, 0xE1, 0xC0, 0x00, 0x03, 0xF8, 0xE0, 0x00,
  0x00, 0xFC, 0x60, 0x00, 0x00, 0x7F, 0x7C, 0x00, 0x00, 0x1F, 0xFF, 0x00,
  0x00, 0x07, 0xFF, 0xE0, 0x00, 0x03, 0xFB, 0xF8, 0x00, 0x00, 0xFE, 0x7F,
  0x00, 0x00, 0x3F, 0x9F, 0xC0, 0x00, 0x0F, 0xE3, 0xF8, 0x00, 0x07, 0xF0,
  0xFE, 0x00, 0x01, 0xFC, 0x1F, 0xC0, 0x00, 0x7F, 0x07, 0xF0, 0x00, 0x1F,
  0x80, 0xFE, 0x00, 0x0F, 0xE0, 0x3F, 0x80, 0x03, 0xF8, 0x0F, 0xE0, 0x00,
  0xFE, 0x01, 0xFC, 0x00, 0x7F, 0x00, 0x7F, 0x00, 0x1F, 0xE0, 0x0F, 0xE0,
  0x0F, 0xF8, 0x07, 0xFC, 0x0F, 0xFF, 0xC7, 0xFF, 0xC0, 0x01, 0xFF, 0xF8,
  0x00, 0x03, 0xFF, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x03, 0xF8, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x0F,
  0xE0, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x3F, 0x80,
  0x00, 0x01, 0xFC, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0xFE, 0x00, 0x00,
  0x07, 0xF0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x7F, 0x00,
  0x00, 0x03, 0xF8, 0x00, 0x04, 0x1F, 0xC0, 0x00, 0x60, 0xFC, 0x00, 0x06,
  0x0F, 0xE0, 0x00, 0x30, 0x7F, 0x00, 0x03, 0x83, 0xF8, 0x00, 0x7C, 0x3F,
  0x80, 0x0F, 0xC1, 0xFE, 0x03, 0xFE, 0x1F, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF,
  0xFF, 0x00, 0x01, 0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0x03, 0xFC, 0x00, 0x03,
  0xFC, 0x00, 0x3F, 0xC0, 0x00, 0x7F, 0x80, 0x03, 0xFC, 0x00, 0x0F, 0xF8,
  0x00, 0x3F, 0xC0, 0x00, 0xFF, 0x80, 0x03, 0xFC, 0x00, 0x1F, 0xF0, 0x00,
  0x6F, 0xC0, 0x03, 0xFF, 0x00, 0x06, 0xFC, 0x00, 0x37, 0xF0, 0x00, 0x6F,
  0xE0, 0x06, 0x7E, 0x00, 0x04, 0xFE, 0x00, 0xEF, 0xE0, 0x00, 0xCF, 0xE0,
  0x0C, 0xFE, 0x00, 0x0C, 0xFE, 0x01, 0x8F, 0xE0, 0x00, 0xCF, 0xE0, 0x38,
  0xFC, 0x00, 0x18, 0x7E, 0x03, 0x1F, 0xC0, 0x01, 0x87, 0xE0, 0x61, 0xFC,
  0x00, 0x18, 0x7E, 0x0E, 0x1F, 0xC0, 0x01, 0x87, 0xE0, 0xC3, 0xF8, 0x00,
  0x30, 0x7F, 0x18, 0x3F, 0x80, 0x03, 0x07, 0xF3, 0x83, 0xF8, 0x00, 0x30,
  0x7F, 0x30, 0x3F, 0x00, 0x06, 0x07, 0xF7, 0x07, 0xF0, 0x00, 0x60, 0x3F,
  0xE0, 0x7F, 0x00, 0x06, 0x03, 0xFC, 0x07, 0xF0, 0x00, 0xE0, 0x3F, 0xC0,
  0x7E, 0x00, 0x0C, 0x03, 0xF8, 0x0F, 0xE0, 0x00, 0xC0, 0x3F, 0x00, 0xFE,
  0x00, 0x0C, 0x03, 0xF0, 0x0F, 0xE0, 0x01, 0xC0, 0x3E, 0x01, 0xFC, 0x00,
  0x1C, 0x03, 0xC0, 0x1F, 0xC0, 0x07, 0xE0, 0x3C, 0x03, 0xFE, 0x00, 0xFF,
  0xC1, 0x81, 0xFF, 0xFC, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x03, 0xFF,
  0x00, 0x1F, 0xF8, 0x03, 0xF8, 0x00, 0x3F, 0x00, 0x0F, 0xE0, 0x00, 0xF0,
  0x00, 0x7F, 0x00, 0x07, 0x00, 0x03, 0xFC, 0x00, 0x38, 0x00, 0x1F, 0xE0,
  0x01, 0x80, 0x01, 0xBF, 0x80, 0x0C, 0x00, 0x0D, 0xFC, 0x00, 0x60, 0x00,
  0x67, 0xF0, 0x07, 0x00, 0x02, 0x3F, 0x80, 0x30, 0x00, 0x30, 0xFE, 0x01,
  0x80, 0x01, 0x87, 0xF0, 0x0C, 0x00, 0x0C, 0x1F, 0xC0, 0xC0, 0x00, 0xC0,
  0xFE, 0x06, 0x00, 0x06, 0x07, 0xF8, 0x30, 0x00, 0x30, 0x1F, 0xC1, 0x80,
  0x01, 0x80, 0xFF, 0x18, 0x00, 0x18, 0x03, 0xF8, 0xC0, 0x00, 0xC0, 0x1F,
  0xC6, 0x00, 0x06, 0x00, 0x7F, 0x60, 0x00, 0x60, 0x03, 0xFB, 0x00, 0x03,
  0x00, 0x0F, 0xF8, 0x00, 0x18, 0x00, 0x7F, 0xC0, 0x01, 0xC0, 0x01, 0xFC,
  0x00, 0x0C, 0x00, 0x0F, 0xE0, 0x00, 0x60, 0x00, 0x3F, 0x00, 0x03, 0x00,
  0x01, 0xF0, 0x00, 0x38, 0x00, 0x07, 0x80, 0x01, 0xC0, 0x00, 0x3C, 0x00,
  0x3F, 0x00, 0x01, 0xE0, 0x03, 0xFF, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00,
  0x30, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0x7E,
  0x1F, 0x80, 0x01, 0xF0, 0x0F, 0x80, 0x0F, 0xC0, 0x1F, 0x80, 0x3F, 0x00,
  0x1F, 0x80, 0xFE, 0x00, 0x3F, 0x03, 0xF8, 0x00, 0x7E, 0x07, 0xF0, 0x00,
  0xFE, 0x1F, 0xC0, 0x01, 0xFC, 0x7F, 0x80, 0x03, 0xF8, 0xFE, 0x00, 0x07,
  0xF3, 0xFC, 0x00, 0x1F, 0xE7, 0xF0, 0x00, 0x3F, 0xDF, 0xE0, 0x00, 0x7F,
  0xBF, 0xC0, 0x00, 0xFE, 0x7F, 0x80, 0x03, 0xFC, 0xFE, 0x00, 0x07, 0xFB,
  0xFC, 0x00, 0x0F, 0xF7, 0xF8, 0x00, 0x3F, 0xCF, 0xF0, 0x00, 0x7F, 0x9F,
  0xC0, 0x00, 0xFE, 0x3F, 0x80, 0x03, 0xFC, 0x7F, 0x00, 0x07, 0xF0, 0xFE,
  0x00, 0x1F, 0xC0, 0xFC, 0x00, 0x3F, 0x81, 0xF8, 0x00, 0xFE, 0x03, 0xF0,
  0x03, 0xF8, 0x03, 0xF0, 0x07, 0xE0, 0x03, 0xE0, 0x1F, 0x00, 0x03, 0xE0,
  0xFC, 0x00, 0x03, 0xFF, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0x80, 0x00, 0xFF, 0xFF, 0xE0, 0x00, 0xFE, 0x1F, 0xE0, 0x01, 0xFC,
  0x1F, 0xE0, 0x03, 0xF0, 0x1F, 0xC0, 0x0F, 0xE0, 0x3F, 0xC0, 0x1F, 0xC0,
  0x7F, 0x80, 0x3F, 0x80, 0xFF, 0x00, 0x7E, 0x01, 0xFE, 0x01, 0xFC, 0x03,
  0xFC, 0x03, 0xF8, 0x0F, 0xF8, 0x07, 0xF0, 0x1F, 0xE0, 0x0F, 0xC0, 0x7F,
  0x80, 0x3F, 0x81, 0xFE, 0x00, 0x7F, 0x07, 0xF8, 0x00, 0xFF, 0xFF, 0xC0,
  0x03, 0xFF, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00,
  0x1F, 0x80, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x01,
  0xFC, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x01, 0xFC,
  0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xFE, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x7E, 0x1F, 0x80, 0x01,
  0xF0, 0x0F, 0x80, 0x0F, 0xC0, 0x1F, 0x80, 0x3F, 0x80, 0x1F, 0x80, 0xFE,
  0x00, 0x3F, 0x03, 0xF8, 0x00, 0x7E, 0x07, 0xF0, 0x00, 0xFE, 0x1F, 0xC0,
  0x01, 0xFC, 0x7F, 0x80, 0x03, 0xF8, 0xFE, 0x00, 0x07, 0xF3, 0xFC, 0x00,
  0x1F, 0xE7, 0xF8, 0x00, 0x3F, 0xDF, 0xE0, 0x00, 0x7F, 0xBF, 0xC0, 0x00,
  0xFF, 0x7F, 0x80, 0x01, 0xFC, 0xFE, 0x00, 0x07, 0xFB, 0xFC, 0x00, 0x0F,
  0xF7, 0xF8, 0x00, 0x1F, 0xCF, 0xF0, 0x00, 0x7F, 0x9F, 0xC0, 0x00, 0xFE,
  0x3F, 0x80, 0x01, 0xFC, 0x7F, 0x00, 0x07, 0xF0, 0xFE, 0x00, 0x0F, 0xE1,
  0xFC, 0x00, 0x3F, 0x81, 0xF8, 0x00, 0x7E, 0x03, 0xF0, 0x01, 0xF8, 0x03,
  0xE0, 0x07, 0xE0, 0x07, 0xE0, 0x1F, 0x80, 0x03, 0xE0, 0x7E, 0x00, 0x03,
  0xF3, 0xF0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x01, 0x80, 0x00, 0x00, 0x06,
  0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0xC0, 0x7F, 0xE0, 0x03, 0x03, 0xFF,
  0xF8, 0x1C, 0x0F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xC0, 0xE0, 0x3F,
  0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x01, 0xFF, 0xFF, 0xC0, 0x00, 0x7F,
  0xFF, 0xF8, 0x00, 0x3F, 0xC3, 0xFC, 0x00, 0x3F, 0x81, 0xFE, 0x00, 0x3F,
  0x80, 0xFF, 0x00, 0x7F, 0x80, 0xFF, 0x00, 0x7F, 0x00, 0xFF, 0x00, 0x7F,
  0x00, 0xFF, 0x00, 0x7F, 0x00, 0xFF, 0x00, 0xFF, 0x01, 0xFE, 0x00, 0xFE,
  0x01, 0xFE, 0x00, 0xFE, 0x03, 0xFC, 0x00, 0xFE, 0x07, 0xF8, 0x01, 0xFC,
  0x1F, 0xF0, 0x01, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0xFE, 0x00, 0x03, 0xFD,
  0xFE, 0x00, 0x03, 0xF8, 0xFF, 0x00, 0x03, 0xF8, 0xFF, 0x00, 0x03, 0xF8,
  0xFF, 0x00, 0x07, 0xF8, 0x7F, 0x80, 0x07, 0xF0, 0x7F, 0x80, 0x07, 0xF0,
  0x3F, 0x80, 0x07, 0xF0, 0x3F, 0xC0, 0x0F, 0xE0, 0x3F, 0xC0, 0x0F, 0xE0,
  0x1F, 0xC0, 0x0F, 0xE0, 0x1F, 0xE0, 0x1F, 0xE0, 0x1F, 0xE0, 0x1F, 0xE0,
  0x0F, 0xF0, 0x3F, 0xF0, 0x0F, 0xF8, 0xFF, 0xFC, 0x0F, 0xFE, 0x00, 0x1F,
  0x83, 0x00, 0x7F, 0xF7, 0x00, 0xF8, 0x7E, 0x01, 0xE0, 0x1E, 0x03, 0xC0,
  0x0E, 0x03, 0xC0, 0x0E, 0x07, 0xC0, 0x0E, 0x07, 0xC0, 0x04, 0x07, 0xC0,
  0x04, 0x07, 0xE0, 0x04, 0x07, 0xF0, 0x00, 0x07, 0xF8, 0x00, 0x03, 0xFC,
  0x00, 0x03, 0xFF, 0x00, 0x01, 0xFF, 0x80, 0x00, 0xFF, 0xC0, 0x00, 0x7F,
  0xE0, 0x00, 0x3F, 0xE0, 0x00, 0x1F, 0xF0, 0x00, 0x0F, 0xF0, 0x00, 0x07,
  0xF8, 0x00, 0x03, 0xF8, 0x00, 0x01, 0xF8, 0x20, 0x00, 0xF8, 0x20, 0x00,
  0xF8, 0x20, 0x00, 0xF8, 0x70, 0x00, 0xF8, 0x70, 0x00, 0xF0, 0x78, 0x01,
  0xF0, 0x78, 0x03, 0xE0, 0x7E, 0x07, 0xC0, 0x47, 0xFF, 0x80, 0xC0, 0xFC,
  0x00, 0x3F, 0xFF, 0xFF, 0xE7, 0xFF, 0xFF, 0xFC, 0xFE, 0x3F, 0x8F, 0x9E,
  0x07, 0xF0, 0xF3, 0x81, 0xFC, 0x0E, 0x60, 0x3F, 0x81, 0x98, 0x07, 0xF0,
  0x13, 0x00, 0xFC, 0x02, 0x00, 0x3F, 0x80, 0x40, 0x07, 0xF0, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00, 0xFE, 0x00,
  0x00, 0x1F, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x03, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00,
  0x03, 0xF8, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xF8,
  0x00, 0x00, 0x7E, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xF8, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x3F, 0xFF, 0xC0, 0x00, 0x7F, 0xFF,
  0x03, 0xFF, 0x0F, 0xFC, 0x00, 0xFC, 0x07, 0xF0, 0x00, 0x38, 0x07, 0xF0,
  0x00, 0x38, 0x07, 0xF0, 0x00, 0x30, 0x0F, 0xE0, 0x00, 0x30, 0x0F, 0xE0,
  0x00, 0x70, 0x0F, 0xE0, 0x00, 0x60, 0x0F, 0xE0, 0x00, 0x60, 0x1F, 0xC0,
  0x00, 0xE0, 0x1F, 0xC0, 0x00, 0xC0, 0x1F, 0xC0, 0x00, 0xC0, 0x3F, 0x80,
  0x00, 0xC0, 0x3F, 0x80, 0x01, 0x80, 0x3F, 0x80, 0x01, 0x80, 0x3F, 0x80,
  0x01, 0x80, 0x7F, 0x00, 0x01, 0x80, 0x7F, 0x00, 0x03, 0x00, 0x7F, 0x00,
  0x03, 0x00, 0x7E, 0x00, 0x03, 0x00, 0xFE, 0x00, 0x06, 0x00, 0xFE, 0x00,
  0x06, 0x00, 0xFC, 0x00, 0x06, 0x00, 0xFC, 0x00, 0x0E, 0x00, 0xFC, 0x00,
  0x0C, 0x00, 0xFC, 0x00, 0x1C, 0x00, 0xFC, 0x00, 0x18, 0x00, 0x7E, 0x00,
  0x38, 0x00, 0x7E, 0x00, 0x70, 0x00, 0x3F, 0x81, 0xE0, 0x00, 0x0F, 0xFF,
  0x80, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF, 0xFC, 0x03, 0xFE, 0x7F, 0xE0,
  0x01, 0xF8, 0x7F, 0x80, 0x01, 0xC0, 0xFF, 0x00, 0x03, 0x80, 0xFE, 0x00,
  0x0E, 0x01, 0xFC, 0x00, 0x18, 0x03, 0xF8, 0x00, 0x70, 0x07, 0xF0, 0x00,
  0xC0, 0x0F, 0xF0, 0x03, 0x80, 0x1F, 0xE0, 0x0E, 0x00, 0x1F, 0xC0, 0x18,
  0x00, 0x3F, 0x80, 0x70, 0x00, 0x7F, 0x00, 0xC0, 0x00, 0xFE, 0x03, 0x00,
  0x01, 0xFC, 0x0E, 0x00, 0x03, 0xF8, 0x18, 0x00, 0x07, 0xF8, 0x60, 0x00,
  0x07, 0xF1, 0xC0, 0x00, 0x0F, 0xE3, 0x00, 0x00, 0x1F, 0xCC, 0x00, 0x00,
  0x3F, 0xB8, 0x00, 0x00, 0x7F, 0x60, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x07,
  0xE0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x3C,
  0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0xFF, 0xF8,
  0xFF, 0xF0, 0xFF, 0x9F, 0xF8, 0x1F, 0xE0, 0x0F, 0x87, 0xF8, 0x07, 0xE0,
  0x07, 0x03, 0xF8, 0x03, 0xF0, 0x03, 0x80, 0xFE, 0x01, 0xF8, 0x01, 0x80,
  0x7F, 0x00, 0xFC, 0x00, 0xC0, 0x3F, 0x80, 0x7F, 0x00, 0xC0, 0x1F, 0xC0,
  0x7F, 0x80, 0x60, 0x0F, 0xE0, 0x3F, 0xC0, 0x60, 0x07, 0xF0, 0x37, 0xE0,
  0x30, 0x03, 0xF8, 0x1B, 0xF0, 0x30, 0x00, 0xFC, 0x19, 0xF8, 0x18, 0x00,
  0x7E, 0x0C, 0xFE, 0x18, 0x00, 0x3F, 0x84, 0x7F, 0x0C, 0x00, 0x1F, 0xC6,
  0x3F, 0x8C, 0x00, 0x0F, 0xE2, 0x1F, 0xC6, 0x00, 0x07, 0xF3, 0x07, 0xE6,
  0x00, 0x03, 0xF9, 0x83, 0xF3, 0x00, 0x01, 0xFD, 0x81, 0xFB, 0x00, 0x00,
  0x7E, 0xC0, 0xFD, 0x80, 0x00, 0x3F, 0xC0, 0x7F, 0x80, 0x00, 0x1F, 0xE0,
  0x3F, 0xC0, 0x00, 0x0F, 0xE0, 0x1F, 0xC0, 0x00, 0x07, 0xF0, 0x0F, 0xE0,
  0x00, 0x03, 0xF0, 0x07, 0xE0, 0x00, 0x01, 0xF8, 0x01, 0xF0, 0x00, 0x00,
  0x78, 0x00, 0xF0, 0x00, 0x00, 0x3C, 0x00, 0x78, 0x00, 0x00, 0x1C, 0x00,
  0x38, 0x00, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x00, 0x06, 0x00, 0x0C, 0x00,
  0x00, 0x03, 0x00, 0x06, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0xFF, 0xC0, 0x3F,
  0xE0, 0x0F, 0xC0, 0x03, 0xF8, 0x01, 0xE0, 0x00, 0xFE, 0x00, 0xE0, 0x00,
  0x3F, 0x80, 0x70, 0x00, 0x07, 0xE0, 0x18, 0x00, 0x01, 0xFC, 0x0C, 0x00,
  0x00, 0x7F, 0x06, 0x00, 0x00, 0x0F, 0xC3, 0x00, 0x00, 0x03, 0xF9, 0x80,
  0x00, 0x00, 0xFE, 0xC0, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x07, 0xF8,
  0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x0F,
  0xC0, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x77, 0xF0, 0x00, 0x00, 0x39, 0xFC, 0x00, 0x00,
  0x1C, 0x3F, 0x00, 0x00, 0x06, 0x0F, 0xE0, 0x00, 0x03, 0x03, 0xF8, 0x00,
  0x01, 0x80, 0x7E, 0x00, 0x00, 0xE0, 0x1F, 0xC0, 0x00, 0x70, 0x07, 0xF0,
  0x00, 0x38, 0x01, 0xFC, 0x00, 0x1E, 0x00, 0x7F, 0x80, 0x1F, 0xC0, 0x1F,
  0xF0, 0x0F, 0xFC, 0x3F, 0xFF, 0x80, 0xFF, 0xF8, 0x3F, 0xF3, 0xFC, 0x00,
  0xFC, 0x1F, 0xC0, 0x07, 0x81, 0xFC, 0x00, 0x70, 0x0F, 0xC0, 0x0E, 0x00,
  0xFE, 0x00, 0xC0, 0x0F, 0xE0, 0x1C, 0x00, 0x7E, 0x03, 0x80, 0x07, 0xF0,
  0x30, 0x00, 0x7F, 0x06, 0x00, 0x03, 0xF0, 0xE0, 0x00, 0x3F, 0x8C, 0x00,
  0x03, 0xF9, 0x80, 0x00, 0x1F, 0xB0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x1F,
  0xE0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x1F, 0x80, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x03,
  0xF8, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x7F, 0x00,
  0x00, 0x07, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x01,
  0xFF, 0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xF0, 0x3F,
  0xFF, 0xFF, 0x03, 0xF8, 0x0F, 0xF0, 0x7C, 0x01, 0xFE, 0x07, 0x80, 0x3F,
  0xC0, 0x70, 0x03, 0xF8, 0x06, 0x00, 0x7F, 0x80, 0xC0, 0x0F, 0xF0, 0x08,
  0x01, 0xFE, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x7F,
  0x80, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00,
  0x03, 0xFC, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x1F, 0xE0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x3F, 0xC0, 0x00,
  0x07, 0xF8, 0x00, 0xC0, 0xFF, 0x00, 0x0C, 0x1F, 0xE0, 0x01, 0x81, 0xFE,
  0x00, 0x38, 0x3F, 0xC0, 0x07, 0x87, 0xF8, 0x01, 0xF0, 0xFF, 0x00, 0xFF,
  0x0F, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xE0, 0x0F,
  0xFC, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x80, 0x01, 0xF0, 0x00, 0x3E,
  0x00, 0x07, 0x80, 0x00, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0xF0,
  0x00, 0x1E, 0x00, 0x07, 0xC0, 0x00, 0xF8, 0x00, 0x1E, 0x00, 0x03, 0xC0,
  0x00, 0xF8, 0x00, 0x1F, 0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x1F, 0x00,
  0x03, 0xE0, 0x00, 0x78, 0x00, 0x0F, 0x00, 0x03, 0xE0, 0x00, 0x7C, 0x00,
  0x0F, 0x00, 0x01, 0xE0, 0x00, 0x7C, 0x00, 0x0F, 0x80, 0x01, 0xE0, 0x00,
  0x3C, 0x00, 0x0F, 0x80, 0x01, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xFE, 0x01,
  0xFF, 0xC0, 0x00, 0xF0, 0x07, 0x80, 0x1E, 0x00, 0xF0, 0x07, 0x80, 0x1C,
  0x00, 0xF0, 0x07, 0x80, 0x3C, 0x00, 0xF0, 0x07, 0x80, 0x3C, 0x01, 0xE0,
  0x07, 0x80, 0x3C, 0x01, 0xE0, 0x07, 0x00, 0x3C, 0x01, 0xE0, 0x0F, 0x00,
  0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x01,
  0xC0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x0F, 0x00, 0x78, 0x00, 0x7F, 0xE0,
  0x0F, 0xFC, 0x00, 0x0F, 0x80, 0x01, 0xE0, 0x00, 0x7C, 0x00, 0x0F, 0x80,
  0x01, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x80, 0x01, 0xF0, 0x00, 0x3E, 0x00,
  0x07, 0x80, 0x01, 0xF0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0xF0, 0x00,
  0x3E, 0x00, 0x07, 0xC0, 0x00, 0xF8, 0x00, 0x1E, 0x00, 0x07, 0xC0, 0x00,
  0xF8, 0x00, 0x1F, 0x00, 0x03, 0xC0, 0x00, 0xF8, 0x00, 0x1F, 0x00, 0x03,
  0xE0, 0x00, 0x78, 0x00, 0x1F, 0x00, 0x03, 0xE0, 0x00, 0x7C, 0x00, 0x0F,
  0x00, 0x01, 0xE0, 0x00, 0x7C, 0x00, 0x0F, 0x80, 0x01, 0xE0, 0x07, 0xFC,
  0x01, 0xFF, 0x80, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xC0, 0x00, 0x7F, 0x00,
  0x03, 0xF8, 0x00, 0x3F, 0xC0, 0x01, 0xEF, 0x00, 0x1E, 0x78, 0x00, 0xF1,
  0xE0, 0x0F, 0x0F, 0x00, 0x78, 0x3C, 0x07, 0xC1, 0xE0, 0x3C, 0x07, 0x83,
  0xE0, 0x3C, 0x1E, 0x00, 0xF1, 0xF0, 0x07, 0x8F, 0x00, 0x1E, 0xF8, 0x00,
  0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x3E,
  0x0F, 0x83, 0xF0, 0x3E, 0x07, 0x80, 0xF0, 0x0E, 0x01, 0xC0, 0x00, 0x3C,
  0x0C, 0x03, 0xF9, 0xF0, 0x1F, 0x3F, 0x80, 0xF8, 0x7E, 0x07, 0xC1, 0xF8,
  0x3F, 0x07, 0xC0, 0xF8, 0x1F, 0x07, 0xE0, 0x7C, 0x3F, 0x01, 0xF0, 0xFC,
  0x0F, 0x87, 0xE0, 0x3E, 0x1F, 0x80, 0xF8, 0x7E, 0x03, 0xC3, 0xF8, 0x1F,
  0x0F, 0xC0, 0x7C, 0x3F, 0x03, 0xF0, 0xFC, 0x0F, 0x83, 0xF0, 0x7E, 0x3F,
  0xC2, 0xF8, 0xBF, 0x9B, 0xE4, 0x7F, 0xCF, 0xE0, 0xFE, 0x3F, 0x01, 0xE0,
  0x78, 0x00, 0x00, 0x7C, 0x00, 0x3F, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0x7E,
  0x00, 0x01, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0x1F, 0x00, 0x00, 0x7C, 0x00,
  0x03, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x3E, 0x3E, 0x01, 0xF9, 0xFC, 0x07,
  0xEF, 0xF8, 0x1F, 0x47, 0xF0, 0x7E, 0x0F, 0xC3, 0xF8, 0x3F, 0x0F, 0xC0,
  0xFC, 0x3F, 0x03, 0xF1, 0xF8, 0x0F, 0xC7, 0xE0, 0x3F, 0x1F, 0x01, 0xF8,
  0x7C, 0x07, 0xE3, 0xF0, 0x1F, 0x8F, 0xC0, 0xFC, 0x3E, 0x03, 0xF1, 0xF8,
  0x0F, 0x87, 0xE0, 0x7C, 0x1F, 0x03, 0xE0, 0xFC, 0x0F, 0x03, 0xF0, 0x78,
  0x0F, 0xC7, 0xC0, 0x1F, 0xFE, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x3F, 0x00,
  0x3F, 0xE0, 0x1E, 0x3C, 0x0F, 0x0F, 0x07, 0x87, 0xC3, 0xE1, 0xF1, 0xF0,
  0x38, 0xFC, 0x00, 0x3E, 0x00, 0x1F, 0x80, 0x07, 0xE0, 0x01, 0xF8, 0x00,
  0xFC, 0x00, 0x3F, 0x00, 0x0F, 0xC0, 0x03, 0xF0, 0x00, 0xFC, 0x03, 0x3F,
  0x00, 0xCF, 0xE0, 0x61, 0xFC, 0x70, 0x3F, 0xF8, 0x07, 0xFC, 0x00, 0xFC,
  0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x0F, 0xC0, 0x00, 0x7F, 0xE0, 0x00,
  0x07, 0xF0, 0x00, 0x03, 0xF0, 0x00, 0x01, 0xF8, 0x00, 0x00, 0xFC, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x1F, 0x80,
  0x07, 0x9F, 0x80, 0x0F, 0xFF, 0xC0, 0x0F, 0x9F, 0xE0, 0x0F, 0x87, 0xF0,
  0x0F, 0x83, 0xF0, 0x0F, 0xC1, 0xF8, 0x07, 0xC0, 0xFC, 0x07, 0xE0, 0x7C,
  0x07, 0xE0, 0x7E, 0x03, 0xF0, 0x3F, 0x03, 0xF0, 0x1F, 0x81, 0xF8, 0x0F,
  0x80, 0xFC, 0x0F, 0xC0, 0xFE, 0x07, 0xE0, 0x7E, 0x07, 0xE0, 0x3F, 0x03,
  0xF0, 0x1F, 0x83, 0xF8, 0x0F, 0xC1, 0xF8, 0xC7, 0xE1, 0xFC, 0xC3, 0xF9,
  0xBE, 0xC0, 0xFF, 0x9F, 0xC0, 0x7F, 0x8F, 0xC0, 0x0F, 0x83, 0xC0, 0x00,
  0x00, 0x3F, 0x00, 0x3F, 0xE0, 0x1E, 0x3C, 0x0F, 0x0F, 0x07, 0x83, 0xC3,
  0xE0, 0xF1, 0xF0, 0x3C, 0xFC, 0x1E, 0x3F, 0x0F, 0x9F, 0x83, 0xC7, 0xE3,
  0xE1, 0xFB, 0xE0, 0xFF, 0xE0, 0x3F, 0xC0, 0x0F, 0xC0, 0x03, 0xF0, 0x00,
  0xFC, 0x03, 0x3F, 0x01, 0x8F, 0xC0, 0xC1, 0xF8, 0x70, 0x7F, 0xF8, 0x07,
  0xFC, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x03, 0xCE, 0x00,
  0x00, 0x78, 0xF0, 0x00, 0x0F, 0x8F, 0x00, 0x00, 0xF0, 0xF0, 0x00, 0x1F,
  0x06, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x03, 0xFF, 0xC0, 0x00, 0x3F,
  0xFC, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0xF8, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x01,
  0xF8, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x3F, 0x00,
  0x00, 0x03, 0xF0, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0,
  0x00, 0x00, 0xFC, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0xF8, 0x00, 0x00,
  0x0F, 0x80, 0x00, 0x01, 0xF0, 0x00, 0x06, 0x1F, 0x00, 0x00, 0xF1, 0xE0,
  0x00, 0x0F, 0x3E, 0x00, 0x00, 0xF3, 0xC0, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x3E, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x7F, 0xF0, 0x00, 0x7E,
  0x3F, 0xE0, 0x7C, 0x0F, 0xF0, 0x7E, 0x07, 0xC0, 0x7E, 0x03, 0xE0, 0x3F,
  0x01, 0xF0, 0x1F, 0x01, 0xF8, 0x0F, 0x80, 0xFC, 0x07, 0xC0, 0xFC, 0x01,
  0xE0, 0xFC, 0x00, 0x78, 0xFC, 0x00, 0x1F, 0xFC, 0x00, 0x0F, 0xF0, 0x00,
  0x1C, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x0F, 0xF8, 0x00,
  0x07, 0xFF, 0x80, 0x01, 0xFF, 0xF8, 0x00, 0x7F, 0xFE, 0x00, 0x77, 0xFF,
  0x80, 0xF0, 0x7F, 0xC0, 0xF0, 0x07, 0xE0, 0xF0, 0x01, 0xF0, 0x78, 0x00,
  0xF8, 0x3C, 0x00, 0x78, 0x1F, 0x00, 0x7C, 0x07, 0xC0, 0x78, 0x01, 0xFF,
  0xF8, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x04, 0x00, 0x01, 0xF8, 0x00, 0x1F,
  0xF0, 0x00, 0x07, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x7E,
  0x00, 0x00, 0xFC, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0xC0,
  0x00, 0x1F, 0x87, 0xC0, 0x3E, 0x1F, 0xC0, 0xFC, 0x7F, 0x81, 0xF9, 0x9F,
  0x03, 0xE6, 0x3E, 0x07, 0xD8, 0x7C, 0x1F, 0xA0, 0xF8, 0x3F, 0x83, 0xF0,
  0x7F, 0x07, 0xE0, 0xFC, 0x0F, 0xC3, 0xF8, 0x3F, 0x07, 0xE0, 0x7E, 0x0F,
  0xC0, 0xFC, 0x3F, 0x03, 0xF0, 0x7E, 0x07, 0xE0, 0xFC, 0x0F, 0xC1, 0xF0,
  0x3F, 0x17, 0xE0, 0x7E, 0x6F, 0xC0, 0xF9, 0x9F, 0x01, 0xF6, 0x3E, 0x03,
  0xF8, 0xFC, 0x07, 0xF1, 0xC0, 0x07, 0x80, 0x01, 0xE0, 0x3F, 0x03, 0xF0,
  0x3F, 0x03, 0xF0, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xC7,
  0xFC, 0x1F, 0xC0, 0xF8, 0x0F, 0x81, 0xF8, 0x1F, 0x81, 0xF0, 0x1F, 0x03,
  0xF0, 0x3E, 0x03, 0xE0, 0x3E, 0x07, 0xE0, 0x7C, 0x07, 0xC0, 0xFC, 0x2F,
  0x84, 0xF8, 0xCF, 0x98, 0xFF, 0x0F, 0xE0, 0x78, 0x00, 0x00, 0x00, 0x78,
  0x00, 0x03, 0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0xFC, 0x00,
  0x01, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x01, 0x00, 0x00, 0xFC, 0x00, 0x1F, 0xF0, 0x00, 0x1F, 0xC0,
  0x00, 0x3E, 0x00, 0x01, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0x1F, 0x80, 0x00,
  0x7C, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0xF8,
  0x00, 0x07, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x7E, 0x00, 0x01, 0xF0, 0x00,
  0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xE0, 0x00, 0x1F,
  0x80, 0x00, 0x7E, 0x00, 0x01, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x3F, 0x00,
  0x60, 0xF8, 0x03, 0xC3, 0xC0, 0x0F, 0x1F, 0x00, 0x3C, 0xF8, 0x00, 0x7F,
  0xC0, 0x00, 0xFC, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0xFC, 0x00, 0x07,
  0xFC, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xF8, 0x00, 0x01,
  0xF8, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x03,
  0xF0, 0x00, 0x03, 0xF0, 0x00, 0x03, 0xE3, 0xFF, 0x03, 0xE0, 0xFC, 0x07,
  0xE0, 0xF0, 0x07, 0xE0, 0xE0, 0x07, 0xC1, 0xC0, 0x0F, 0xC3, 0x80, 0x0F,
  0xC7, 0x00, 0x0F, 0x8E, 0x00, 0x0F, 0xBE, 0x00, 0x1F, 0xFE, 0x00, 0x1F,
  0xFE, 0x00, 0x1F, 0xFE, 0x00, 0x1F, 0x3E, 0x00, 0x3F, 0x3F, 0x00, 0x3F,
  0x1F, 0x00, 0x3E, 0x1F, 0x00, 0x7E, 0x1F, 0x04, 0x7E, 0x1F, 0x8C, 0x7E,
  0x0F, 0x98, 0x7C, 0x0F, 0xF0, 0xFC, 0x07, 0xE0, 0xE0, 0x03, 0xC0, 0x00,
  0x08, 0x0F, 0xC7, 0xFE, 0x07, 0xF0, 0x3F, 0x01, 0xF8, 0x0F, 0xC0, 0x7C,
  0x07, 0xE0, 0x3F, 0x01, 0xF8, 0x0F, 0x80, 0x7C, 0x07, 0xE0, 0x3E, 0x01,
  0xF0, 0x1F, 0x80, 0xFC, 0x07, 0xC0, 0x3E, 0x03, 0xF0, 0x1F, 0x80, 0xF8,
  0x0F, 0xC0, 0x7E, 0x03, 0xE0, 0x1F, 0x00, 0xF8, 0x8F, 0x8C, 0x7C, 0x43,
  0xE4, 0x1F, 0xE0, 0xFE, 0x03, 0xC0, 0x00, 0x00, 0x70, 0x78, 0x0F, 0x83,
  0xFE, 0x3F, 0x87, 0xF8, 0x1F, 0xCF, 0xF1, 0xFF, 0x03, 0xF1, 0x3E, 0x73,
  0xE0, 0x7E, 0x47, 0xD8, 0x7C, 0x0F, 0xD0, 0xFB, 0x1F, 0x81, 0xF4, 0x3E,
  0xC3, 0xF0, 0x3E, 0x87, 0xF0, 0x7C, 0x0F, 0xE0, 0xFE, 0x1F, 0x81, 0xF4,
  0x1F, 0x83, 0xF0, 0x3F, 0x07, 0xE0, 0x7C, 0x07, 0xE0, 0xFC, 0x1F, 0x81,
  0xF8, 0x1F, 0x83, 0xF0, 0x3F, 0x07, 0xE0, 0x7C, 0x07, 0xE0, 0xFC, 0x0F,
  0x80, 0xF8, 0x1F, 0x03, 0xF0, 0x3F, 0x07, 0xE0, 0x7E, 0x07, 0xE0, 0xFC,
  0x0F, 0x88, 0xF8, 0x1F, 0x81, 0xF3, 0x3F, 0x03, 0xE0, 0x3E, 0x47, 0xE0,
  0xFC, 0x07, 0xF0, 0xFC, 0x1F, 0x80, 0xFE, 0x18, 0x00, 0x00, 0x0F, 0x00,
  0x00, 0x70, 0xF8, 0x7F, 0xC3, 0xF8, 0x1F, 0x8F, 0xF0, 0x3F, 0x33, 0xE0,
  0x7C, 0x87, 0xC1, 0xF9, 0x0F, 0x83, 0xF4, 0x1F, 0x07, 0xD0, 0x3E, 0x0F,
  0xE0, 0xFC, 0x3F, 0x81, 0xF8, 0x7F, 0x03, 0xE0, 0xFC, 0x0F, 0xC1, 0xF8,
  0x1F, 0x87, 0xE0, 0x3E, 0x0F, 0xC0, 0xFC, 0x1F, 0x81, 0xF0, 0x3E, 0x03,
  0xE0, 0xFC, 0x0F, 0xC9, 0xF8, 0x1F, 0x33, 0xE0, 0x3E, 0x47, 0xC0, 0x7F,
  0x1F, 0x80, 0xFE, 0x38, 0x00, 0xF0, 0x00, 0x00, 0x3F, 0x00, 0x0E, 0x38,
  0x03, 0xC1, 0xC0, 0x78, 0x1E, 0x0F, 0x81, 0xF0, 0xF0, 0x1F, 0x1F, 0x01,
  0xF3, 0xE0, 0x1F, 0x3E, 0x03, 0xF7, 0xC0, 0x3F, 0x7C, 0x03, 0xF7, 0xC0,
  0x3E, 0xFC, 0x03, 0xEF, 0xC0, 0x7E, 0xF8, 0x07, 0xCF, 0x80, 0x7C, 0xF8,
  0x0F, 0x8F, 0x80, 0xF8, 0xF8, 0x1F, 0x07, 0x81, 0xE0, 0x78, 0x3C, 0x03,
  0xC7, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x0F, 0x1F, 0x00, 0x3F, 0xE7, 0xF8,
  0x01, 0xF9, 0xFF, 0x00, 0x1F, 0x47, 0xF0, 0x07, 0xF0, 0x7E, 0x00, 0xFE,
  0x0F, 0xC0, 0x1F, 0x81, 0xF8, 0x03, 0xF0, 0x3F, 0x00, 0xFC, 0x07, 0xE0,
  0x1F, 0x81, 0xFC, 0x03, 0xE0, 0x3F, 0x00, 0x7C, 0x07, 0xE0, 0x1F, 0x81,
  0xFC, 0x03, 0xF0, 0x3F, 0x00, 0x7C, 0x07, 0xE0, 0x0F, 0x81, 0xF8, 0x03,
  0xF0, 0x3E, 0x00, 0x7E, 0x0F, 0xC0, 0x0F, 0x81, 0xF0, 0x01, 0xF0, 0x7C,
  0x00, 0x7F, 0x1F, 0x00, 0x0F, 0xFF, 0xC0, 0x01, 0xF3, 0xE0, 0x00, 0x3E,
  0x00, 0x00, 0x0F, 0xC0, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x3E, 0x00, 0x00,
  0x0F, 0xC0, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x3F, 0xFC,
  0x00, 0x00, 0x00, 0x3E, 0x00, 0x03, 0xF9, 0xF0, 0x1F, 0x1F, 0xC0, 0xF8,
  0x7E, 0x07, 0xC1, 0xF8, 0x3F, 0x07, 0xE0, 0xF8, 0x1F, 0x87, 0xE0, 0x7C,
  0x3F, 0x01, 0xF0, 0xFC, 0x0F, 0xC7, 0xE0, 0x3E, 0x1F, 0x80, 0xF8, 0x7E,
  0x07, 0xE3, 0xF0, 0x1F, 0x8F, 0xC0, 0x7C, 0x3F, 0x03, 0xF0, 0xFC, 0x0F,
  0xC3, 0xF0, 0x7E, 0x0F, 0xC3, 0xF8, 0x3F, 0x9B, 0xE0, 0x7F, 0xDF, 0x01,
  0xFE, 0x7C, 0x01, 0xF1, 0xF0, 0x00, 0x0F, 0xC0, 0x00, 0x3E, 0x00, 0x00,
  0xF8, 0x00, 0x07, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x7C, 0x00, 0x03, 0xF8,
  0x00, 0x7F, 0xF8, 0x00, 0x00, 0x71, 0xE1, 0xFF, 0x3E, 0x07, 0xE7, 0xF0,
  0x7E, 0xFF, 0x07, 0xE9, 0xE0, 0x7D, 0x0E, 0x07, 0xD0, 0x00, 0xFE, 0x00,
  0x0F, 0xE0, 0x00, 0xFC, 0x00, 0x0F, 0xC0, 0x01, 0xFC, 0x00, 0x1F, 0x80,
  0x01, 0xF8, 0x00, 0x1F, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x03, 0xF0,
  0x00, 0x7E, 0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0x07, 0xC0, 0x00, 0x01,
  0xF1, 0x07, 0xFF, 0x0F, 0x0F, 0x0E, 0x07, 0x1E, 0x06, 0x1E, 0x06, 0x1F,
  0x02, 0x1F, 0x02, 0x1F, 0x80, 0x0F, 0xC0, 0x0F, 0xE0, 0x0F, 0xF0, 0x07,
  0xF8, 0x03, 0xF8, 0x01, 0xFC, 0x00, 0xFC, 0x40, 0x7C, 0x40, 0x7C, 0x60,
  0x3C, 0xE0, 0x38, 0xF0, 0x38, 0xF8, 0xF0, 0xDF, 0xC0, 0x00, 0x20, 0x03,
  0x00, 0x38, 0x03, 0x80, 0x3C, 0x03, 0xE0, 0x7F, 0x07, 0xFF, 0x3F, 0xF8,
  0x7C, 0x07, 0xE0, 0x3F, 0x01, 0xF0, 0x0F, 0x80, 0xFC, 0x07, 0xC0, 0x3E,
  0x03, 0xF0, 0x1F, 0x80, 0xF8, 0x07, 0xC0, 0x7E, 0x03, 0xF1, 0x1F, 0x08,
  0xF8, 0x87, 0xC8, 0x3F, 0xC1, 0xFC, 0x07, 0x80, 0x00, 0x00, 0x40, 0x00,
  0x1F, 0x03, 0xF7, 0xF8, 0x0F, 0x87, 0xE0, 0x3E, 0x1F, 0x81, 0xF8, 0x7E,
  0x07, 0xC1, 0xF0, 0x1F, 0x07, 0xC0, 0xFC, 0x3F, 0x03, 0xE0, 0xF8, 0x0F,
  0x83, 0xE0, 0x7E, 0x0F, 0x81, 0xF8, 0x7E, 0x0F, 0xC1, 0xF0, 0x3F, 0x07,
  0xC1, 0xFC, 0x1F, 0x07, 0xE0, 0xF8, 0x2F, 0x83, 0xE1, 0x3C, 0x6F, 0x8D,
  0xF1, 0x3E, 0x67, 0xC8, 0xFF, 0x1F, 0xE3, 0xF8, 0x7F, 0x07, 0xC0, 0xF0,
  0x00, 0x06, 0x07, 0x1F, 0x07, 0xBF, 0x83, 0xE7, 0xC1, 0xF3, 0xE0, 0xF9,
  0xF8, 0x3C, 0x7C, 0x0C, 0x3E, 0x06, 0x1F, 0x03, 0x0F, 0x83, 0x07, 0xC1,
  0x83, 0xE1, 0x81, 0xF1, 0x80, 0xF9, 0x80, 0x7C, 0xC0, 0x3E, 0xC0, 0x1F,
  0xC0, 0x0F, 0xC0, 0x07, 0xC0, 0x03, 0xC0, 0x01, 0xC0, 0x00, 0xC0, 0x00,
  0x40, 0x00, 0x06, 0x01, 0x81, 0xC7, 0xC0, 0x30, 0x7F, 0xF8, 0x0E, 0x0F,
  0x9F, 0x01, 0xC1, 0xF3, 0xE0, 0x78, 0x3E, 0x7C, 0x1F, 0x03, 0xCF, 0xC3,
  0xE0, 0x30, 0xF8, 0xFC, 0x06, 0x1F, 0x1F, 0xC0, 0x83, 0xE7, 0xF8, 0x30,
  0x7C, 0xFF, 0x04, 0x0F, 0xB7, 0xE1, 0x81, 0xF6, 0xFC, 0x60, 0x3F, 0x8F,
  0x98, 0x07, 0xE1, 0xF3, 0x00, 0xFC, 0x3E, 0xC0, 0x1F, 0x07, 0xF0, 0x03,
  0xE0, 0xFC, 0x00, 0x78, 0x1F, 0x80, 0x0F, 0x03, 0xE0, 0x01, 0xC0, 0x78,
  0x00, 0x30, 0x0E, 0x00, 0x06, 0x01, 0x80, 0x00, 0x00, 0xF0, 0x1E, 0x0F,
  0xF0, 0x3E, 0x01, 0xF8, 0x7F, 0x01, 0xF8, 0xFF, 0x00, 0xF9, 0x8E, 0x00,
  0xFB, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0x7C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xFE, 0x00, 0x01, 0xBF, 0x00, 0x01,
  0xBF, 0x08, 0x73, 0x1F, 0x18, 0xFF, 0x1F, 0x30, 0xFE, 0x1F, 0xE0, 0xFC,
  0x0F, 0xC0, 0x78, 0x07, 0x80, 0x00, 0x30, 0x1C, 0x0F, 0xF0, 0x7C, 0x07,
  0xE0, 0xF8, 0x0F, 0xC1, 0xF0, 0x0F, 0xC1, 0xE0, 0x1F, 0x81, 0xC0, 0x3F,
  0x03, 0x00, 0x3E, 0x06, 0x00, 0x7E, 0x08, 0x00, 0xFC, 0x30, 0x01, 0xF8,
  0x60, 0x01, 0xF1, 0x80, 0x03, 0xE3, 0x00, 0x07, 0xCC, 0x00, 0x0F, 0xD8,
  0x00, 0x1F, 0xE0, 0x00, 0x1F, 0xC0, 0x00, 0x3F, 0x00, 0x00, 0x7E, 0x00,
  0x00, 0xF8, 0x00, 0x01, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x07, 0x00, 0x00,
  0x0C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x60, 0x01, 0xC1, 0x80, 0x07, 0xE6,
  0x00, 0x0F, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x07,
  0xFF, 0xE1, 0xFF, 0xF8, 0x3F, 0xFF, 0x07, 0xFF, 0xC0, 0x80, 0x70, 0x30,
  0x1C, 0x04, 0x07, 0x00, 0x00, 0xC0, 0x00, 0x38, 0x00, 0x0E, 0x00, 0x03,
  0x80, 0x00, 0x60, 0x00, 0x18, 0x00, 0x06, 0x00, 0x01, 0xC0, 0x00, 0x30,
  0x00, 0x0C, 0x00, 0x03, 0xE0, 0x00, 0xFE, 0x00, 0x1F, 0xE0, 0xC7, 0xFC,
  0x3D, 0xCF, 0xC7, 0x90, 0xF8, 0xF0, 0x07, 0x9C, 0x00, 0x3E, 0x00, 0x00,
  0x01, 0xF0, 0x00, 0xFC, 0x00, 0x1F, 0x00, 0x03, 0xE0, 0x00, 0x7C, 0x00,
  0x07, 0xC0, 0x00, 0x78, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00, 0x0F, 0x80,
  0x01, 0xF0, 0x00, 0x1F, 0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x03, 0xE0,
  0x00, 0x3E, 0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x0F, 0xC0,
  0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00, 0x0F,
  0x80, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x03,
  0xE0, 0x00, 0x3E, 0x00, 0x03, 0xE0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00,
  0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7E, 0x00,
  0x03, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00,
  0x3E, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xE0, 0x00, 0x3E, 0x00, 0x03, 0xE0,
  0x00, 0x3E, 0x00, 0x03, 0xE0, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7C,
  0x00, 0x07, 0xC0, 0x00, 0xF8, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00, 0x0F,
  0x80, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x00,
  0xF8, 0x00, 0x03, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x03, 0xE0, 0x00,
  0x7C, 0x00, 0x07, 0xC0, 0x00, 0xF8, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00,
  0x0F, 0x80, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00,
  0x03, 0xE0, 0x00, 0x3E, 0x00, 0x07, 0xC0, 0x00, 0x7C, 0x00, 0x0F, 0x80,
  0x03, 0xF0, 0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x03, 0xFF, 0x01, 0x3F,
  0xFE, 0x1D, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0x00, 0x1F, 0xF0, 0x00, 0x1F,
  0x00 };

const GFXglyph FreeSerifBoldItalic24pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  12,    0,    1 },   // 0x20 ' '
  {     0,  15,  33,  18,    3,  -31 },   // 0x21 '!'
  {    62,  19,  13,  26,    6,  -31 },   // 0x22 '"'
  {    93,  27,  33,  23,   -2,  -32 },   // 0x23 '#'
  {   205,  24,  39,  24,   -1,  -33 },   // 0x24 '$'
  {   322,  35,  32,  39,    2,  -30 },   // 0x25 '%'
  {   462,  33,  33,  37,    0,  -31 },   // 0x26 '&'
  {   599,   7,  13,  13,    6,  -31 },   // 0x27 '''
  {   611,  14,  41,  16,    1,  -31 },   // 0x28 '('
  {   683,  14,  41,  16,   -2,  -31 },   // 0x29 ')'
  {   755,  19,  20,  23,    3,  -31 },   // 0x2A '*'
  {   803,  22,  23,  27,    2,  -22 },   // 0x2B '+'
  {   867,  10,  15,  12,   -3,   -5 },   // 0x2C ','
  {   886,  12,   5,  16,    0,  -12 },   // 0x2D '-'
  {   894,   8,   7,  12,    0,   -5 },   // 0x2E '.'
  {   901,  19,  33,  16,    0,  -31 },   // 0x2F '/'
  {   980,  22,  33,  23,    1,  -31 },   // 0x30 '0'
  {  1071,  20,  32,  23,    0,  -31 },   // 0x31 '1'
  {  1151,  22,  32,  23,    1,  -31 },   // 0x32 '2'
  {  1239,  22,  33,  24,    0,  -31 },   // 0x33 '3'
  {  1330,  25,  32,  23,    0,  -31 },   // 0x34 '4'
  {  1430,  24,  32,  24,    0,  -30 },   // 0x35 '5'
  {  1526,  23,  32,  24,    1,  -30 },   // 0x36 '6'
  {  1618,  23,  31,  23,    3,  -30 },   // 0x37 '7'
  {  1708,  21,  33,  23,    1,  -31 },   // 0x38 '8'
  {  1795,  23,  33,  23,    0,  -31 },   // 0x39 '9'
  {  1890,  13,  22,  12,    0,  -20 },   // 0x3A ':'
  {  1926,  15,  30,  12,   -2,  -20 },   // 0x3B ';'
  {  1983,  24,  25,  27,    1,  -23 },   // 0x3C '<'
  {  2058,  24,  14,  27,    3,  -18 },   // 0x3D '='
  {  2100,  24,  25,  27,    3,  -23 },   // 0x3E '>'
  {  2175,  18,  33,  24,    4,  -31 },   // 0x3F '?'
  {  2250,  33,  33,  39,    3,  -31 },   // 0x40 '@'
  {  2387,  31,  32,  33,    0,  -31 },   // 0x41 'A'
  {  2511,  31,  31,  30,    0,  -30 },   // 0x42 'B'
  {  2632,  29,  33,  29,    2,  -31 },   // 0x43 'C'
  {  2752,  35,  31,  34,    0,  -30 },   // 0x44 'D'
  {  2888,  32,  31,  30,    0,  -30 },   // 0x45 'E'
  {  3012,  31,  31,  29,    0,  -30 },   // 0x46 'F'
  {  3133,  32,  33,  33,    2,  -31 },   // 0x47 'G'
  {  3265,  39,  31,  35,    0,  -30 },   // 0x48 'H'
  {  3417,  21,  31,  18,    0,  -30 },   // 0x49 'I'
  {  3499,  27,  36,  23,    0,  -30 },   // 0x4A 'J'
  {  3621,  34,  31,  31,    0,  -30 },   // 0x4B 'K'
  {  3753,  29,  31,  29,    0,  -30 },   // 0x4C 'L'
  {  3866,  44,  32,  41,    0,  -30 },   // 0x4D 'M'
  {  4042,  37,  32,  33,    0,  -30 },   // 0x4E 'N'
  {  4190,  31,  33,  32,    2,  -31 },   // 0x4F 'O'
  {  4318,  31,  31,  28,    0,  -30 },   // 0x50 'P'
  {  4439,  31,  42,  32,    2,  -31 },   // 0x51 'Q'
  {  4602,  32,  31,  31,    0,  -30 },   // 0x52 'R'
  {  4726,  24,  33,  24,    0,  -31 },   // 0x53 'S'
  {  4825,  27,  31,  28,    4,  -30 },   // 0x54 'T'
  {  4930,  32,  32,  34,    5,  -30 },   // 0x55 'U'
  {  5058,  31,  32,  33,    6,  -30 },   // 0x56 'V'
  {  5182,  41,  32,  44,    6,  -30 },   // 0x57 'W'
  {  5346,  34,  31,  33,    0,  -30 },   // 0x58 'X'
  {  5478,  28,  31,  30,    6,  -30 },   // 0x59 'Y'
  {  5587,  28,  31,  26,    0,  -30 },   // 0x5A 'Z'
  {  5696,  19,  38,  16,   -2,  -30 },   // 0x5B '['
  {  5787,  13,  33,  19,    6,  -31 },   // 0x5C '\'
  {  5841,  19,  38,  16,   -3,  -30 },   // 0x5D ']'
  {  5932,  21,  17,  27,    3,  -30 },   // 0x5E '^'
  {  5977,  24,   3,  23,    0,    5 },   // 0x5F '_'
  {  5986,  10,   9,  16,    4,  -32 },   // 0x60 '`'
  {  5998,  22,  23,  24,    1,  -21 },   // 0x61 'a'
  {  6062,  22,  33,  23,    1,  -31 },   // 0x62 'b'
  {  6153,  18,  23,  20,    1,  -21 },   // 0x63 'c'
  {  6205,  25,  34,  24,    1,  -32 },   // 0x64 'd'
  {  6312,  18,  23,  20,    1,  -21 },   // 0x65 'e'
  {  6364,  28,  41,  23,   -4,  -31 },   // 0x66 'f'
  {  6508,  25,  31,  23,   -1,  -21 },   // 0x67 'g'
  {  6605,  23,  34,  26,    1,  -32 },   // 0x68 'h'
  {  6703,  12,  33,  14,    2,  -31 },   // 0x69 'i'
  {  6753,  22,  42,  16,   -4,  -31 },   // 0x6A 'j'
  {  6869,  24,  34,  24,    1,  -32 },   // 0x6B 'k'
  {  6971,  13,  34,  14,    2,  -32 },   // 0x6C 'l'
  {  7027,  35,  23,  36,    0,  -21 },   // 0x6D 'm'
  {  7128,  23,  23,  25,    0,  -21 },   // 0x6E 'n'
  {  7195,  20,  23,  22,    1,  -21 },   // 0x6F 'o'
  {  7253,  27,  31,  23,   -4,  -21 },   // 0x70 'p'
  {  7358,  22,  31,  23,    1,  -21 },   // 0x71 'q'
  {  7444,  20,  22,  19,    0,  -21 },   // 0x72 'r'
  {  7499,  16,  23,  17,    0,  -21 },   // 0x73 's'
  {  7545,  13,  29,  13,    2,  -27 },   // 0x74 't'
  {  7593,  22,  23,  25,    2,  -21 },   // 0x75 'u'
  {  7657,  17,  23,  21,    3,  -21 },   // 0x76 'v'
  {  7706,  27,  23,  31,    3,  -21 },   // 0x77 'w'
  {  7784,  24,  23,  22,   -1,  -21 },   // 0x78 'x'
  {  7853,  23,  31,  20,   -3,  -21 },   // 0x79 'y'
  {  7943,  19,  25,  19,    0,  -20 },   // 0x7A 'z'
  {  8003,  20,  41,  16,    0,  -31 },   // 0x7B '{'
  {  8106,   4,  33,  13,    5,  -31 },   // 0x7C '|'
  {  8123,  20,  41,  16,   -6,  -31 },   // 0x7D '}'
  {  8226,  21,   7,  27,    3,  -14 } }; // 0x7E '~'

const GFXfont FreeSerifBoldItalic24pt7b PROGMEM = {
  (uint8_t  *)FreeSerifBoldItalic24pt7bBitmaps,
  (GFXglyph *)FreeSerifBoldItalic24pt7bGlyphs,
  0x20, 0x7E, 56 };

// Approx. 8917 bytes

const uint8_t FreeSans9pt7bBitmaps[] PROGMEM = {
  0xFF, 0xFF, 0xF8, 0xC0, 0xDE, 0xF7, 0x20, 0x09, 0x86, 0x41, 0x91, 0xFF,
  0x13, 0x04, 0xC3, 0x20, 0xC8, 0xFF, 0x89, 0x82, 0x61, 0x90, 0x10, 0x1F,
  0x14, 0xDA, 0x3D, 0x1E, 0x83, 0x40, 0x78, 0x17, 0x08, 0xF4, 0x7A, 0x35,
  0x33, 0xF0, 0x40, 0x20, 0x38, 0x10, 0xEC, 0x20, 0xC6, 0x20, 0xC6, 0x40,
  0xC6, 0x40, 0x6C, 0x80, 0x39, 0x00, 0x01, 0x3C, 0x02, 0x77, 0x02, 0x63,
  0x04, 0x63, 0x04, 0x77, 0x08, 0x3C, 0x0E, 0x06, 0x60, 0xCC, 0x19, 0x81,
  0xE0, 0x18, 0x0F, 0x03, 0x36, 0xC2, 0xD8, 0x73, 0x06, 0x31, 0xE3, 0xC4,
  0xFE, 0x13, 0x26, 0x6C, 0xCC, 0xCC, 0xC4, 0x66, 0x23, 0x10, 0x8C, 0x46,
  0x63, 0x33, 0x33, 0x32, 0x66, 0x4C, 0x80, 0x25, 0x7E, 0xA5, 0x00, 0x30,
  0xC3, 0x3F, 0x30, 0xC3, 0x0C, 0xD6, 0xF0, 0xC0, 0x08, 0x44, 0x21, 0x10,
  0x84, 0x42, 0x11, 0x08, 0x00, 0x3C, 0x66, 0x42, 0xC3, 0xC3, 0xC3, 0xC3,
  0xC3, 0xC3, 0xC3, 0x42, 0x66, 0x3C, 0x11, 0x3F, 0x33, 0x33, 0x33, 0x33,
  0x30, 0x3E, 0x31, 0xB0, 0x78, 0x30, 0x18, 0x1C, 0x1C, 0x1C, 0x18, 0x18,
  0x10, 0x08, 0x07, 0xF8, 0x3C, 0x66, 0xC3, 0xC3, 0x03, 0x06, 0x1C, 0x07,
  0x03, 0xC3, 0xC3, 0x66, 0x3C, 0x0C, 0x18, 0x71, 0x62, 0xC9, 0xA3, 0x46,
  0xFE, 0x18, 0x30, 0x60, 0xC0, 0x7F, 0x20, 0x10, 0x08, 0x08, 0x07, 0xF3,
  0x8C, 0x03, 0x01, 0x80, 0xF0, 0x6C, 0x63, 0xE0, 0x1E, 0x31, 0x98, 0x78,
  0x0C, 0x06, 0xF3, 0x8D, 0x83, 0xC1, 0xE0, 0xD0, 0x6C, 0x63, 0xE0, 0xFF,
  0x03, 0x02, 0x06, 0x04, 0x0C, 0x08, 0x18, 0x18, 0x18, 0x10, 0x30, 0x30,
  0x3E, 0x31, 0xB0, 0x78, 0x3C, 0x1B, 0x18, 0xF8, 0xC6, 0xC1, 0xE0, 0xF0,
  0x6C, 0x63, 0xE0, 0x3C, 0x66, 0xC2, 0xC3, 0xC3, 0xC3, 0x67, 0x3B, 0x03,
  0x03, 0xC2, 0x66, 0x3C, 0xC0, 0x00, 0x30, 0xC0, 0x00, 0x00, 0x64, 0xA0,
  0x00, 0x81, 0xC7, 0x8E, 0x0C, 0x07, 0x80, 0x70, 0x0E, 0x01, 0x80, 0xFF,
  0x80, 0x00, 0x1F, 0xF0, 0x00, 0x70, 0x0E, 0x01, 0xC0, 0x18, 0x38, 0x71,
  0xC0, 0x80, 0x00, 0x3E, 0x31, 0xB0, 0x78, 0x30, 0x18, 0x18, 0x38, 0x18,
  0x18, 0x0C, 0x00, 0x00, 0x01, 0x80, 0x03, 0xF0, 0x06, 0x0E, 0x06, 0x01,
  0x86, 0x00, 0x66, 0x1D, 0xBB, 0x31, 0xCF, 0x18, 0xC7, 0x98, 0x63, 0xCC,
  0x31, 0xE6, 0x11, 0xB3, 0x99, 0xCC, 0xF7, 0x86, 0x00, 0x01, 0x80, 0x00,
  0x70, 0x40, 0x0F, 0xE0, 0x06, 0x00, 0xF0, 0x0F, 0x00, 0x90, 0x19, 0x81,
  0x98, 0x10, 0x83, 0x0C, 0x3F, 0xC2, 0x04, 0x60, 0x66, 0x06, 0xC0, 0x30,
  0xFF, 0x18, 0x33, 0x03, 0x60, 0x6C, 0x0D, 0x83, 0x3F, 0xC6, 0x06, 0xC0,
  0x78, 0x0F, 0x01, 0xE0, 0x6F, 0xF8, 0x1F, 0x86, 0x19, 0x81, 0xA0, 0x3C,
  0x01, 0x80, 0x30, 0x06, 0x00, 0xC0, 0x68, 0x0D, 0x83, 0x18, 0x61, 0xF0,
  0xFF, 0x18, 0x33, 0x03, 0x60, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0,
  0x78, 0x0F, 0x03, 0x60, 0xCF, 0xF0, 0xFF, 0xE0, 0x30, 0x18, 0x0C, 0x06,
  0x03, 0xFD, 0x80, 0xC0, 0x60, 0x30, 0x18, 0x0F, 0xF8, 0xFF, 0xC0, 0xC0,
  0xC0, 0xC0, 0xC0, 0xFE, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0x0F, 0x83,
  0x0E, 0x60, 0x66, 0x03, 0xC0, 0x0C, 0x00, 0xC1, 0xFC, 0x03, 0xC0, 0x36,
  0x03, 0x60, 0x73, 0x0F, 0x0F, 0x10, 0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x3C,
  0x07, 0x80, 0xFF, 0xFE, 0x03, 0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x06,
  0xFF, 0xFF, 0xFF, 0xC0, 0x06, 0x0C, 0x18, 0x30, 0x60, 0xC1, 0x83, 0x07,
  0x8F, 0x1E, 0x27, 0x80, 0xC0, 0xD8, 0x33, 0x0C, 0x63, 0x0C, 0xC1, 0xB8,
  0x3F, 0x07, 0x30, 0xC3, 0x18, 0x63, 0x06, 0x60, 0x6C, 0x0C, 0xC0, 0xC0,
  0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xFF, 0xE0,
  0x3F, 0x01, 0xFC, 0x1F, 0xE0, 0xFD, 0x05, 0xEC, 0x6F, 0x63, 0x79, 0x13,
  0xCD, 0x9E, 0x6C, 0xF1, 0x47, 0x8E, 0x3C, 0x71, 0x80, 0xE0, 0x7C, 0x0F,
  0xC1, 0xE8, 0x3D, 0x87, 0x98, 0xF1, 0x1E, 0x33, 0xC3, 0x78, 0x6F, 0x07,
  0xE0, 0x7C, 0x0E, 0x0F, 0x81, 0x83, 0x18, 0x0C, 0xC0, 0x6C, 0x01, 0xE0,
  0x0F, 0x00, 0x78, 0x03, 0xC0, 0x1B, 0x01, 0x98, 0x0C, 0x60, 0xC0, 0xF8,
  0x00, 0xFF, 0x30, 0x6C, 0x0F, 0x03, 0xC0, 0xF0, 0x6F, 0xF3, 0x00, 0xC0,
  0x30, 0x0C, 0x03, 0x00, 0xC0, 0x00, 0x0F, 0x81, 0x83, 0x18, 0x0C, 0xC0,
  0x6C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x1B, 0x01, 0x98, 0x6C,
  0x60, 0xC0, 0xFB, 0x00, 0x08, 0xFF, 0x8C, 0x0E, 0xC0, 0x6C, 0x06, 0xC0,
  0x6C, 0x0C, 0xFF, 0x8C, 0x0E, 0xC0, 0x6C, 0x06, 0xC0, 0x6C, 0x06, 0xC0,
  0x70, 0x3F, 0x18, 0x6C, 0x0F, 0x03, 0xC0, 0x1E, 0x01, 0xF0, 0x0E, 0x00,
  0xF0, 0x3C, 0x0D, 0x86, 0x3F, 0x00, 0xFF, 0x86, 0x03, 0x01, 0x80, 0xC0,
  0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x01, 0x80, 0xC0, 0xC0, 0x78, 0x0F,
  0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x01,
  0xB0, 0x61, 0xF0, 0xC0, 0x6C, 0x0D, 0x81, 0x10, 0x63, 0x0C, 0x61, 0x04,
  0x60, 0xCC, 0x19, 0x01, 0x60, 0x3C, 0x07, 0x00, 0x60, 0xC1, 0x81, 0x30,
  0xE1, 0x98, 0x70, 0xCC, 0x28, 0x66, 0x26, 0x21, 0x13, 0x30, 0xC8, 0x98,
  0x6C, 0x4C, 0x14, 0x34, 0x0A, 0x1A, 0x07, 0x07, 0x03, 0x03, 0x80, 0x81,
  0x80, 0x60, 0x63, 0x0C, 0x30, 0xC1, 0x98, 0x0F, 0x00, 0xE0, 0x06, 0x00,
  0xF0, 0x19, 0x01, 0x98, 0x30, 0xC6, 0x0E, 0x60, 0x60, 0xC0, 0x36, 0x06,
  0x30, 0xC3, 0x0C, 0x19, 0x81, 0xD8, 0x0F, 0x00, 0x60, 0x06, 0x00, 0x60,
  0x06, 0x00, 0x60, 0x06, 0x00, 0xFF, 0xC0, 0x60, 0x30, 0x0C, 0x06, 0x03,
  0x01, 0xC0, 0x60, 0x30, 0x18, 0x06, 0x03, 0x00, 0xFF, 0xC0, 0xFB, 0x6D,
  0xB6, 0xDB, 0x6D, 0xB6, 0xE0, 0x84, 0x10, 0x84, 0x10, 0x84, 0x10, 0x84,
  0x10, 0x80, 0xED, 0xB6, 0xDB, 0x6D, 0xB6, 0xDB, 0xE0, 0x30, 0x60, 0xA2,
  0x44, 0xD8, 0xA1, 0x80, 0xFF, 0xC0, 0xC6, 0x30, 0x7E, 0x71, 0xB0, 0xC0,
  0x60, 0xF3, 0xDB, 0x0D, 0x86, 0xC7, 0x3D, 0xC0, 0xC0, 0x60, 0x30, 0x1B,
  0xCE, 0x36, 0x0F, 0x07, 0x83, 0xC1, 0xE0, 0xF0, 0x7C, 0x6D, 0xE0, 0x3C,
  0x66, 0xC3, 0xC0, 0xC0, 0xC0, 0xC0, 0xC3, 0x66, 0x3C, 0x03, 0x03, 0x03,
  0x3B, 0x67, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0x67, 0x3B, 0x3C, 0x66,
  0xC3, 0xC3, 0xFF, 0xC0, 0xC0, 0xC3, 0x66, 0x3C, 0x36, 0x6F, 0x66, 0x66,
  0x66, 0x66, 0x60, 0x3B, 0x67, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0x67,
  0x3B, 0x03, 0x03, 0xC6, 0x7C, 0xC0, 0xC0, 0xC0, 0xDE, 0xE3, 0xC3, 0xC3,
  0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xFF, 0xFF, 0xC0, 0x30, 0x03,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0xE0, 0xC0, 0x60, 0x30, 0x18, 0x4C,
  0x46, 0x63, 0x61, 0xF0, 0xEC, 0x62, 0x31, 0x98, 0x6C, 0x30, 0xFF, 0xFF,
  0xFF, 0xC0, 0xDE, 0xF7, 0x1C, 0xF0, 0xC7, 0x86, 0x3C, 0x31, 0xE1, 0x8F,
  0x0C, 0x78, 0x63, 0xC3, 0x1E, 0x18, 0xC0, 0xDE, 0xE3, 0xC3, 0xC3, 0xC3,
  0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0x3C, 0x66, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
  0xC3, 0x66, 0x3C, 0xDE, 0x71, 0xB0, 0x78, 0x3C, 0x1E, 0x0F, 0x07, 0x83,
  0xE3, 0x6F, 0x30, 0x18, 0x0C, 0x00, 0x3B, 0x67, 0xC3, 0xC3, 0xC3, 0xC3,
  0xC3, 0xC3, 0x67, 0x3B, 0x03, 0x03, 0x03, 0xDF, 0x31, 0x8C, 0x63, 0x18,
  0xC6, 0x00, 0x3E, 0xE3, 0xC0, 0xC0, 0xE0, 0x3C, 0x07, 0xC3, 0xE3, 0x7E,
  0x66, 0xF6, 0x66, 0x66, 0x66, 0x67, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3, 0xC3,
  0xC3, 0xC3, 0xC7, 0x7B, 0xC1, 0xA0, 0x98, 0xCC, 0x42, 0x21, 0xB0, 0xD0,
  0x28, 0x1C, 0x0C, 0x00, 0xC6, 0x1E, 0x38, 0x91, 0xC4, 0xCA, 0x66, 0xD3,
  0x16, 0xD0, 0xA6, 0x87, 0x1C, 0x38, 0xC0, 0xC6, 0x00, 0x43, 0x62, 0x36,
  0x1C, 0x18, 0x1C, 0x3C, 0x26, 0x62, 0x43, 0xC1, 0x21, 0x98, 0xCC, 0x42,
  0x61, 0xB0, 0xD0, 0x38, 0x1C, 0x0C, 0x06, 0x03, 0x01, 0x03, 0x00, 0xFE,
  0x0C, 0x30, 0xC1, 0x86, 0x18, 0x20, 0xC1, 0xFC, 0x36, 0x66, 0x66, 0x6E,
  0xCE, 0x66, 0x66, 0x66, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0xC6, 0x66,
  0x66, 0x67, 0x37, 0x66, 0x66, 0x66, 0xC0, 0x61, 0x24, 0x38 };

const GFXglyph FreeSans9pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   5,    0,    1 },   // 0x20 ' '
  {     0,   2,  13,   6,    2,  -12 },   // 0x21 '!'
  {     4,   5,   4,   6,    1,  -12 },   // 0x22 '"'
  {     7,  10,  12,  10,    0,  -11 },   // 0x23 '#'
  {    22,   9,  16,  10,    1,  -13 },   // 0x24 '$'
  {    40,  16,  13,  16,    1,  -12 },   // 0x25 '%'
  {    66,  11,  13,  12,    1,  -12 },   // 0x26 '&'
  {    84,   2,   4,   4,    1,  -12 },   // 0x27 '''
  {    85,   4,  17,   6,    1,  -12 },   // 0x28 '('
  {    94,   4,  17,   6,    1,  -12 },   // 0x29 ')'
  {   103,   5,   5,   7,    1,  -12 },   // 0x2A '*'
  {   107,   6,   8,  11,    3,   -7 },   // 0x2B '+'
  {   113,   2,   4,   5,    2,    0 },   // 0x2C ','
  {   114,   4,   1,   6,    1,   -4 },   // 0x2D '-'
  {   115,   2,   1,   5,    1,    0 },   // 0x2E '.'
  {   116,   5,  13,   5,    0,  -12 },   // 0x2F '/'
  {   125,   8,  13,  10,    1,  -12 },   // 0x30 '0'
  {   138,   4,  13,  10,    3,  -12 },   // 0x31 '1'
  {   145,   9,  13,  10,    1,  -12 },   // 0x32 '2'
  {   160,   8,  13,  10,    1,  -12 },   // 0x33 '3'
  {   173,   7,  13,  10,    2,  -12 },   // 0x34 '4'
  {   185,   9,  13,  10,    1,  -12 },   // 0x35 '5'
  {   200,   9,  13,  10,    1,  -12 },   // 0x36 '6'
  {   215,   8,  13,  10,    0,  -12 },   // 0x37 '7'
  {   228,   9,  13,  10,    1,  -12 },   // 0x38 '8'
  {   243,   8,  13,  10,    1,  -12 },   // 0x39 '9'
  {   256,   2,  10,   5,    1,   -9 },   // 0x3A ':'
  {   259,   3,  12,   5,    1,   -8 },   // 0x3B ';'
  {   264,   9,   9,  11,    1,   -8 },   // 0x3C '<'
  {   275,   9,   4,  11,    1,   -5 },   // 0x3D '='
  {   280,   9,   9,  11,    1,   -8 },   // 0x3E '>'
  {   291,   9,  13,  10,    1,  -12 },   // 0x3F '?'
  {   306,  17,  16,  18,    1,  -12 },   // 0x40 '@'
  {   340,  12,  13,  12,    0,  -12 },   // 0x41 'A'
  {   360,  11,  13,  12,    1,  -12 },   // 0x42 'B'
  {   378,  11,  13,  13,    1,  -12 },   // 0x43 'C'
  {   396,  11,  13,  13,    1,  -12 },   // 0x44 'D'
  {   414,   9,  13,  11,    1,  -12 },   // 0x45 'E'
  {   429,   8,  13,  11,    1,  -12 },   // 0x46 'F'
  {   442,  12,  13,  14,    1,  -12 },   // 0x47 'G'
  {   462,  11,  13,  13,    1,  -12 },   // 0x48 'H'
  {   480,   2,  13,   5,    2,  -12 },   // 0x49 'I'
  {   484,   7,  13,  10,    1,  -12 },   // 0x4A 'J'
  {   496,  11,  13,  12,    1,  -12 },   // 0x4B 'K'
  {   514,   8,  13,  10,    1,  -12 },   // 0x4C 'L'
  {   527,  13,  13,  15,    1,  -12 },   // 0x4D 'M'
  {   549,  11,  13,  13,    1,  -12 },   // 0x4E 'N'
  {   567,  13,  13,  14,    1,  -12 },   // 0x4F 'O'
  {   589,  10,  13,  12,    1,  -12 },   // 0x50 'P'
  {   606,  13,  14,  14,    1,  -12 },   // 0x51 'Q'
  {   629,  12,  13,  13,    1,  -12 },   // 0x52 'R'
  {   649,  10,  13,  12,    1,  -12 },   // 0x53 'S'
  {   666,   9,  13,  11,    1,  -12 },   // 0x54 'T'
  {   681,  11,  13,  13,    1,  -12 },   // 0x55 'U'
  {   699,  11,  13,  12,    0,  -12 },   // 0x56 'V'
  {   717,  17,  13,  17,    0,  -12 },   // 0x57 'W'
  {   745,  12,  13,  12,    0,  -12 },   // 0x58 'X'
  {   765,  12,  13,  12,    0,  -12 },   // 0x59 'Y'
  {   785,  10,  13,  11,    1,  -12 },   // 0x5A 'Z'
  {   802,   3,  17,   5,    1,  -12 },   // 0x5B '['
  {   809,   5,  13,   5,    0,  -12 },   // 0x5C '\'
  {   818,   3,  17,   5,    0,  -12 },   // 0x5D ']'
  {   825,   7,   7,   8,    1,  -12 },   // 0x5E '^'
  {   832,  10,   1,  10,    0,    3 },   // 0x5F '_'
  {   834,   4,   3,   5,    0,  -12 },   // 0x60 '`'
  {   836,   9,  10,  10,    1,   -9 },   // 0x61 'a'
  {   848,   9,  13,  10,    1,  -12 },   // 0x62 'b'
  {   863,   8,  10,   9,    1,   -9 },   // 0x63 'c'
  {   873,   8,  13,  10,    1,  -12 },   // 0x64 'd'
  {   886,   8,  10,  10,    1,   -9 },   // 0x65 'e'
  {   896,   4,  13,   5,    1,  -12 },   // 0x66 'f'
  {   903,   8,  14,  10,    1,   -9 },   // 0x67 'g'
  {   917,   8,  13,  10,    1,  -12 },   // 0x68 'h'
  {   930,   2,  13,   4,    1,  -12 },   // 0x69 'i'
  {   934,   4,  17,   4,    0,  -12 },   // 0x6A 'j'
  {   943,   9,  13,   9,    1,  -12 },   // 0x6B 'k'
  {   958,   2,  13,   4,    1,  -12 },   // 0x6C 'l'
  {   962,  13,  10,  15,    1,   -9 },   // 0x6D 'm'
  {   979,   8,  10,  10,    1,   -9 },   // 0x6E 'n'
  {   989,   8,  10,  10,    1,   -9 },   // 0x6F 'o'
  {   999,   9,  13,  10,    1,   -9 },   // 0x70 'p'
  {  1014,   8,  13,  10,    1,   -9 },   // 0x71 'q'
  {  1027,   5,  10,   6,    1,   -9 },   // 0x72 'r'
  {  1034,   8,  10,   9,    1,   -9 },   // 0x73 's'
  {  1044,   4,  12,   5,    1,  -11 },   // 0x74 't'
  {  1050,   8,  10,  10,    1,   -9 },   // 0x75 'u'
  {  1060,   9,  10,   9,    0,   -9 },   // 0x76 'v'
  {  1072,  13,  10,  13,    0,   -9 },   // 0x77 'w'
  {  1089,   8,  10,   9,    0,   -9 },   // 0x78 'x'
  {  1099,   9,  14,   9,    0,   -9 },   // 0x79 'y'
  {  1115,   7,  10,   9,    1,   -9 },   // 0x7A 'z'
  {  1124,   4,  17,   6,    1,  -12 },   // 0x7B '{'
  {  1133,   2,  17,   4,    2,  -12 },   // 0x7C '|'
  {  1138,   4,  17,   6,    1,  -12 },   // 0x7D '}'
  {  1147,   7,   3,   9,    1,   -7 } }; // 0x7E '~'

const GFXfont FreeSans9pt7b PROGMEM = {
  (uint8_t  *)FreeSans9pt7bBitmaps,
  (GFXglyph *)FreeSans9pt7bGlyphs,
  0x20, 0x7E, 22 };

// Approx. 1822 bytes

Old and debug examples.

AnalogLogger - A simple data logger for one or more analog pins.

append - This sketch creates a large file by successive
         open/write/close operations.

average - A demonstration of parsing floating point numbers.

BaseExtCaseTest - Long file name test.

benchSD - A read/write benchmark for the standard Arduino SD.h library.

bufstream - ibufsteam to parse a line and obufstream to format a line.

cin_cout - Demo of ArduinoInStream and ArduinoOutStream.             

eventlog - Append a line to a file - demo of pathnames and streams.

fgetsRewrite - Demo of rewriting a line read by fgets.

HelloWorld - Create a serial output stream.

MiniSerial - SdFat minimal serial support for debug.

PrintBenchmarkSD - Bench mark SD.h print.

readlog - Read file. Demo of pathnames and current working directory.

SD_Size - Determine flash used by SD.h example.

SdFatSize - Determine flash used by SdFat.

StreamParseInt - Simple demo of the Stream parsInt() member function.
